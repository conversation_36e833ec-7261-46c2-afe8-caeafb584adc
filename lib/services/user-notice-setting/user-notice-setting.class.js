"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserNoticeSetting = void 0;
const feathers_mongoose_1 = require("feathers-mongoose");
const { NotFound, GeneralError, BadRequest } = require('@feathersjs/errors');
class UserNoticeSetting extends feathers_mongoose_1.Service {
    //eslint-disable-next-line @typescript-eslint/no-unused-vars
    constructor(options, app) {
        super(options);
        this.app = app;
    }
    async getSave({ id, enable, setting }, params) {
        var _a;
        let uid = id || ((_a = params.user) === null || _a === void 0 ? void 0 : _a._id);
        if (!uid)
            throw new NotFound('No user');
        let data = await this.Model.findById(uid);
        let res;
        if (!data) {
            res = await this.Model.create({ _id: uid, enable, setting });
        }
        else {
            let settingUpdate = {};
            if (!data.setting) {
                settingUpdate = setting;
            }
            else {
                settingUpdate = data.setting;
                for (let key in setting) {
                    settingUpdate[key] = setting[key];
                }
            }
            res = await this.Model.updateOne({ _id: uid }, { enable, setting: settingUpdate });
        }
        return res;
    }
    async checkStatus(id, code) {
        let enableEmail = true, enableInbox = true, enableSms = true, enablePush = true;
        let setting = false;
        if (!id) {
            return { enableEmail, enableInbox, enableSms, enablePush };
        }
        try {
            setting = await this.Model.findById(id);
        }
        catch (error) { }
        if (setting && setting.enable === false) {
            enableEmail = false;
            enableInbox = false;
            enableSms = false;
            enablePush = false;
        }
        else if (setting && setting.enable === true) {
            if (setting.setting) {
                const tpl = await this.app.service('notice-tpl').Model.findOne({ code });
                if (setting.setting[tpl._id.toString()]) {
                    enableEmail = setting.setting[tpl._id.toString()].enableEmail;
                    enableInbox = setting.setting[tpl._id.toString()].enableInbox;
                    enableSms = setting.setting[tpl._id.toString()].enableSms;
                    enablePush = setting.setting[tpl._id.toString()].enablePush;
                }
            }
        }
        return { enableEmail, enableInbox, enableSms, enablePush };
    }
}
exports.UserNoticeSetting = UserNoticeSetting;
