"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SuspendLogs = void 0;
const feathers_mongoose_1 = require("feathers-mongoose");
const { NotFound, GeneralError, BadRequest } = require('@feathersjs/errors');
class SuspendLogs extends feathers_mongoose_1.Service {
    //eslint-disable-next-line @typescript-eslint/no-unused-vars
    constructor(options, app) {
        super(options);
        this.app = app;
    }
    // 日志撤销
    async getWithdraw({ accident, uid }) {
        let suspend = await this.app.service('suspend-class').Model.findOne({ uid }).sort({ endAt: -1 });
        if (!suspend) {
            throw new NotFound('No suspend found for this user');
        }
        const log = await this.Model.findOne({ accident, uid, withdraw: false }).sort({ endAt: -1 });
        if (!log) {
            throw new NotFound('No log found for this accident');
        }
        let endAt = new Date(suspend.endAt).getTime() - log.days * 24 * 60 * 60 * 1000;
        await this.app.service('suspend-class').Model.updateOne({ _id: suspend._id }, { endAt });
        return await this.Model.updateOne({ _id: log._id }, { withdraw: true });
    }
    async extUser(one, params) {
        if (one.uid) {
            one.userInfo = await this.app.service('users').uidToInfo(one.uid);
        }
    }
}
exports.SuspendLogs = SuspendLogs;
