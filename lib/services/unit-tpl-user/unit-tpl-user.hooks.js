"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
const authentication = __importStar(require("@feathersjs/authentication"));
// Don't remove this comment. It's needed to format import lines nicely.
const { authenticate } = authentication.hooks;
exports.default = {
    before: {
        all: [authenticate('jwt')],
        find: [
            (d) => {
                var _a;
                const query = d.params.query || {};
                query.school = query.school || ((_a = d.params.user) === null || _a === void 0 ? void 0 : _a._id);
            },
        ],
        get: [],
        create: [
            async (d) => {
                var _a;
                d.data.school = d.data.school || ((_a = d.params.user) === null || _a === void 0 ? void 0 : _a._id);
                const { unit, task } = await d.app.service('unit-tpl').copyTpl({ curricId: d.data.curriculum, school: d.data.school }, d.params);
                d.data.tpl = {
                    unit: unit._id,
                    task: task._id,
                };
            },
        ],
        update: [],
        patch: [],
        remove: [],
    },
    after: {
        all: [],
        find: [],
        get: [],
        create: [],
        update: [],
        patch: [],
        remove: [
            async (d) => {
                var _a;
                const uid = (_a = d.params.user) === null || _a === void 0 ? void 0 : _a._id;
                const { _id: tplId, tpl, school } = d.result;
                if (tpl === null || tpl === void 0 ? void 0 : tpl.unit)
                    await d.app.service('unit-tpl').Model.deleteOne({ _id: tpl.unit, school: { $ne: '1' } });
                if (tpl === null || tpl === void 0 ? void 0 : tpl.task)
                    await d.app.service('unit-tpl').Model.deleteOne({ _id: tpl.task, school: { $ne: '1' } });
                // reset default tpl
                const confDoc = await d.app.service('conf-user').Model.findOne({ uid, key: 'UnitTplDefault' });
                if (confDoc) {
                    const key = school === uid ? 'personal' : school;
                    if (confDoc.val[key] === tplId)
                        await d.app.service('conf-user').Model.updateOne({ _id: confDoc._id }, { $unset: { [`val.${key}`]: 1 } });
                }
            },
        ],
    },
    error: {
        all: [],
        find: [],
        get: [],
        create: [],
        update: [],
        patch: [],
        remove: [],
    },
};
