"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const authentication = __importStar(require("@feathersjs/authentication"));
const hook_1 = __importDefault(require("../../hook"));
// Don't remove this comment. It's needed to format import lines nicely.
const { authenticate } = authentication.hooks;
const search = require('feathers-mongodb-fuzzy-search');
function nameFormat(d) {
    if (Array.isArray(d.data)) {
        for (const one of d.data) {
            d.service.nameFormat(one);
        }
    }
    else {
        d.service.nameFormat(d.data);
    }
}
exports.default = {
    before: {
        all: [authenticate('jwt')],
        find: [
            search({
                fields: ['name', 'email'],
            }),
        ],
        get: [hook_1.default.toClass],
        create: [
            async (d) => {
                let num = 0, school = '';
                if (Array.isArray(d.data)) {
                    school = d.data[0].school;
                    num = d.data.length;
                    // todo batch create
                }
                else if (d.data.school) {
                    num = 1;
                    school = d.data.school;
                }
                if (!num)
                    return await Promise.reject(new Error('Error data'));
                await d.app.service('users').checkPlanLimit({ school, num, type: 'teacher' }, d.params);
                setTimeout(() => {
                    d.app.get('redis').HDEL('StatSchool', school).then();
                }, 100);
            },
            nameFormat,
        ],
        update: [],
        patch: [
            nameFormat,
            async (d) => {
                // check plan limit
                if (Acan.isEmpty(d.data.del))
                    return;
                const { school } = (await d.service.Model.findOne({ _id: d.id }).select('school')) || {};
                if (school) {
                    // restore user need check plan limit
                    if (!d.data.del)
                        await d.app.service('users').checkPlanLimit({ school, num: 1, type: 'teacher' }, d.params);
                    d.app.get('redis').HDEL('StatSchool', school).then();
                }
            },
        ],
        remove: [],
    },
    after: {
        all: [],
        find: [],
        get: [],
        create: [
            async (d) => {
                var _a, _b;
                if (d.result.message)
                    return d;
                const arr = ((_a = d.result) === null || _a === void 0 ? void 0 : _a._id) ? [d.result] : Array.isArray(d.result) ? d.result : null;
                if (!arr)
                    return (d.result = { message: 'create error' }), d;
                if ((_b = d.data.role) === null || _b === void 0 ? void 0 : _b.includes('admin'))
                    d.service.send(d.result, 'SchoolTeacherAdminInvite', d.params);
                else if (!d.data.status)
                    d.service.getResend(arr, d.params);
                // 新增统计学校老师数量
                await d.service.count({ school: arr[0].school }, d.params);
                // 单个老师新增，有班级需要统计班级
                if (!Acan.isEmpty(d.data.class))
                    await d.service.countClass({ class: d.data.class }, d.params);
            },
        ],
        update: [],
        patch: [
            async (d) => {
                var _a;
                const { $addToSet, $pull, status } = d.data;
                const { name, email, school } = d.result;
                d.app.get('redis').HDEL('schoolUser:Info' + school, email);
                if ((_a = $addToSet === null || $addToSet === void 0 ? void 0 : $addToSet.role) === null || _a === void 0 ? void 0 : _a.includes('admin'))
                    d.service.send(d.result, 'SchoolTeacherAdminSet', d.params);
                if (($pull === null || $pull === void 0 ? void 0 : $pull.role) === 'admin')
                    d.service.send(d.result, 'SchoolTeacherAdminRemove', d.params);
                // (archive teacher or remove teacher from class) need remove scheduled session
                const uid = d.app.service('users').emailToUid(email);
                if ($pull === null || $pull === void 0 ? void 0 : $pull.class) {
                    await d.service.countClass({ class: $pull.class }, d.params);
                    d.service.cleanUserSession({ uid, classId: $pull.class }, d.params);
                }
                else if (d.data.del)
                    d.service.cleanUserSession({ uid, school }, d.params);
                if (Acan.isDefined(d.data.del))
                    await d.service.count(d.result, d.params);
                if ($addToSet === null || $addToSet === void 0 ? void 0 : $addToSet.class)
                    await d.service.countClass({ class: $addToSet.class }, d.params);
                if (status == 2) {
                    const schoolData = d.app.get('trainingSchool');
                    if (schoolData._id == school) {
                        const url = `${SiteUrl}/v2/home/<USER>
                        d.app.service('notice-tpl').mailto('ClasscipeTrainingCenterIsApproved', email, { url });
                    }
                    else {
                        d.service.send(d.result, 'SchoolTeacherApproved', d.params);
                    }
                }
            },
        ],
        remove: [
            async (d) => {
                if (!d.result)
                    return;
                const { school, email, nickname, uid } = Array.isArray(d.result) ? d.result[0] : d.result;
                if (school)
                    await d.app.get('redis').HDEL('StatSchool', school);
                await d.service.count(d.result, d.params);
                await d.service.countClass({ class: d.result.class }, d.params);
                // unset default tpl set
                for (const one of Array.isArray(d.result) ? d.result : [d.result]) {
                    await d.app.service('conf-user').unsetBySchool(one, d.params);
                }
                d.service.send(d.result, 'teacherremovedfromschool', d.params);
            },
        ],
    },
    error: {
        all: [],
        find: [],
        get: [],
        create: [],
        update: [],
        patch: [],
        remove: [],
    },
};
