"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const authentication = __importStar(require("@feathersjs/authentication"));
const hook_1 = __importDefault(require("../../hook"));
// Don't remove this comment. It's needed to format import lines nicely.
const { authenticate } = authentication.hooks;
exports.default = {
    before: {
        all: [authenticate('jwt')],
        find: [
            hook_1.default.userQuery(),
            hook_1.default.selectList,
            (d) => {
                var _a;
                const query = (_a = d.params.query) !== null && _a !== void 0 ? _a : {};
                if (!(query === null || query === void 0 ? void 0 : query.$isSchool)) {
                    hook_1.default.sysQuery()(d);
                }
                else {
                    delete query.$isSchool;
                }
                if (!query.pid)
                    query.pid = null; // 默认只获取主服务包，排除Lecture包
            },
        ],
        get: [hook_1.default.toClass],
        create: [],
        update: [hook_1.default.disable],
        patch: [hook_1.default.sysQuery(), hook_1.default.toClass],
        remove: [hook_1.default.sysQuery()],
    },
    after: {
        all: [],
        find: [
            async (d) => {
                if (d.result)
                    for (const o of d.result.data) {
                        await d.service.extUser(o);
                    }
                if (isDev)
                    d.result.query = d.params.query;
            },
        ],
        get: [
            async (d) => {
                if (hook_1.default.classExist(d))
                    return d;
                await d.service.extUser(d.result);
            },
        ],
        create: [],
        update: [],
        patch: [
            async (d) => {
                const { _id, total, used, status, snapshot } = d.result;
                // 次数用完了自动更新状态
                if (used === total && status)
                    await d.service.patch(_id, { status: false });
                // status 变化的时候, 服务包有效统计更新
                if (Acan.isDefined(d.data.status) && d.data.status !== status) {
                    await d.app.service('service-pack').incCount(snapshot._id, { 'count.valid': status ? 1 : -1 });
                }
            },
        ],
        remove: [],
    },
    error: {
        all: [],
        find: [],
        get: [],
        create: [],
        update: [],
        patch: [],
        remove: [],
    },
};
