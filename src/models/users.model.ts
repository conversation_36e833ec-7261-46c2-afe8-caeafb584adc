// users-model.ts - A mongoose model
// @ts-nocheck
// See http://mongoosejs.com/docs/models.html
// for more of what you can do here.
import {Application} from '../declarations'
import {Model, Mongoose} from 'mongoose'

export default function (app: Application): Model<any> {
  const modelName = 'users'
  const mongooseClient: Mongoose = app.get('mongooseClient')
  const {Schema} = mongooseClient
  const schema = new Schema(
    {
      name: [{type: String, sparse: true, trim: true}], // [ firstname, lastname ]
      nickname: {type: String, sparse: true, trim: true},
      password: {type: String, sparse: true},
      avatar: {type: String, sparse: true},
      email: {type: String, lowercase: true, trim: true, sparse: true, unique: true},
      mobile: {type: String, trim: true, sparse: true, unique: true}, // E.164 format, maximum of 15 digits, +1001XXX5550100, +************ => +44201234567
      countryCode: {type: String, lowercase: true, trim: true, sparse: true}, // country code, AU, NZ
      roles: {type: [String], sparse: true, enum: Agl.usersRoles}, // room.user.roles
      managerRoles: {type: [String], sparse: true, enum: Agl.usersManagerRoles}, // room.user.roles
      gender: {type: String, sparse: true, trim: true}, // gender
      intro: {type: String, sparse: true, trim: true}, // self intro
      google: {type: String, sparse: true, unique: true}, // google id_token.sub
      zoom: {type: String, sparse: true, unique: true}, // zoom uuid
      lang: {type: String, trim: true, default: 'en-US'}, // navigator.language
      timeZone: {type: String, trim: true}, // Intl.DateTimeFormat().resolvedOptions().timeZone
      tz: {type: Number, trim: true}, // new Date().getTimezoneOffset()
      ip: {type: String}, // reg ip
      last: {type: Date}, // last login time
      emergencyContact: {type: String, trim: true, sparse: true}, // E.164 format, maximum of 15 digits, +1001XXX5550100, +************ => +44201234567
      // for teacher
      teacherExt: {
        curriculum: {type: [String], trim: true},
        subjects: {type: [String], trim: true},
        grades: {type: Schema.Types.Mixed, trim: true},
      },
      // for student
      studentId: {type: String, lowercase: true, trim: true, sparse: true, unique: true}, // studentId + password login
      studentExt: {
        dob: {type: String, trim: true},
        parent: {
          mobile: {type: String, trim: true}, // E.164 format
          email: {type: String, trim: true},
        },
        curriculum: {type: String, trim: true},
        subjects: {type: [String], trim: true},
        grades: {type: [String], trim: true},
      },
      // for service
      freeServiceType: {type: Schema.Types.Mixed}, // 免费使用过的服务类型, 服务包 id 作为key, order 作为 value {[servicePack._id]: order._id}
      inviteCode: {type: String, trim: true}, //邀请码
      inviter: {type: String, trim: true}, //邀请我的人
      point: {type: Number, default: 0}, // 积分
      balance: {type: Number, default: 0}, // 佣金+收入 美分
      freeServiceClaimed: {type: Boolean, default: false}, // 已领取免费服务包
      freePromptCount: {type: Number, default: 0}, // Prompts免费购买次数统计
      agreedToTerms: {type: Boolean, default: false}, // terms and conditions agreed to verification page
      serviceCalendarEmail: {type: Boolean, default: false}, // service-auth审批通过时且认证项包含4个中的一个,每个用户通知一次,#6056 通知155
      giftCardBalance: {type: Number, default: 0}, // Gift card balance in cents
    },
    {
      timestamps: true,
    }
  )

  // This is necessary to avoid model compilation errors in watch mode
  // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
  if (mongooseClient.modelNames().includes(modelName)) {
    ;(mongooseClient as any).deleteModel(modelName)
  }
  return mongooseClient.model<any>(modelName, schema)
}
