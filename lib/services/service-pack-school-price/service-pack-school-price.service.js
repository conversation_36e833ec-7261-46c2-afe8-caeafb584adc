"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const service_pack_school_price_class_1 = require("./service-pack-school-price.class");
const service_pack_school_price_model_1 = __importDefault(require("../../models/service-pack-school-price.model"));
const service_pack_school_price_hooks_1 = __importDefault(require("./service-pack-school-price.hooks"));
function default_1(app) {
    const options = {
        Model: (0, service_pack_school_price_model_1.default)(app),
        paginate: app.get('paginate'),
    };
    // Initialize our service with any options it requires
    app.use('/service-pack-school-price', new service_pack_school_price_class_1.ServicePackSchoolPrice(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('service-pack-school-price');
    service.hooks(service_pack_school_price_hooks_1.default);
}
exports.default = default_1;
