"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SessionSnapshot = void 0;
const feathers_mongoose_1 = require("feathers-mongoose");
const logger_1 = __importDefault(require("../../logger"));
const errors_1 = require("@feathersjs/errors");
class SessionSnapshot extends feathers_mongoose_1.Service {
    //eslint-disable-next-line @typescript-eslint/no-unused-vars
    constructor(options, app) {
        super(options);
        this.selectList = [
            'sid',
            'createBy',
            'createdAt',
            'updatedAt',
            'students',
            'members',
            'teachers',
            'pages._id',
            'pages.pic',
            'questions.type',
            'questions.page',
            'questions.multi',
            'questions.options',
            'response',
        ];
        this.app = app;
    }
    async getStudentData({ _id, student }, params) {
        return await this.snapshot({ _id, student }, params);
    }
    async getSnapshot({ _id, createBy }, params) {
        return await this.snapshot({ _id, createBy, isSave: true }, params);
    }
    async snapshot({ _id, student, isSave, createBy }, params) {
        var _a, _b;
        // 只能创建一次，直接返回已经创建过的数据
        const old = await this.Model.findById(_id);
        if (old)
            return old;
        const doc = Acan.clone(await this.app.service('session').Model.findOne({ _id }).select(['sid', 'school', 'classId', 'materials', 'questions', 'task.pages', 'type']));
        // 获取学生数据
        const students = Acan.clone(await this.app.service('session').getToolMembers({ sid: doc.sid }, params));
        // 没有学生不生成
        if (Acan.isEmpty(students))
            return Promise.reject(new errors_1.GeneralError('no students'));
        if ((_a = doc.task) === null || _a === void 0 ? void 0 : _a.pages) {
            doc.pages = doc.task.pages;
            delete doc.task.pages;
        }
        else if (doc.type === 'videoSession') {
            doc.pages = [];
        }
        // 获取互动题的大纲标签打分配置
        let questions;
        const questionList = {};
        if (doc.questions) {
            const $in = (_b = doc.questions) === null || _b === void 0 ? void 0 : _b.map((v) => v._id);
            const questionsSelect = ['bloom', 'dimension', 'verb', 'tags', 'score', 'tips', 'outlines'];
            questions = Acan.clone(await this.app.service('questions').Model.find({ _id: { $in } }).select(questionsSelect));
            for (const q of questions) {
                questionList[q._id] = q;
            }
            for (const q of doc.questions) {
                Object.assign(q, questionList[q._id]);
            }
        }
        // 获取进入过课堂的学生数据
        const { members, teachers } = (await this.app.service('rooms').Model.findOne({ sid: doc.sid }).select(['members', 'teachers'])) || {};
        // 更新session 统计
        if (isSave)
            await this.app.service('session').Model.updateOne({ _id }, { 'count.students': students.length });
        logger_1.default.info('create session snapshot', students);
        // 获取每个学生的互动数据
        const responseSelect = ['uid', 'nickname', 'page', 'type', 'content', 'answer', 'locked', 'point', 'updatedAt'];
        const responseQuery = student ? { sid: doc.sid, uid: student } : { sid: doc.sid };
        const commentsQuery = student ? { sid: doc.sid, from: student } : { sid: doc.sid };
        const response = Acan.clone(await this.app.service('response').Model.find(responseQuery).select(responseSelect).limit(5000));
        const comments = Acan.clone(await this.app.service('comments').Model.find(commentsQuery).limit(5000));
        let rs = { createBy: createBy || 'user', ...doc, members, teachers, students, response, comments };
        this.stats(rs);
        if (isSave)
            rs = await this.create(rs);
        for (const one of students) {
            if (student && one._id !== student)
                continue; // 指定学生不匹配则不获取数据
            const post = {
                session: doc._id,
                school: doc.school,
                classId: doc.classId,
                uid: one._id,
                response: response === null || response === void 0 ? void 0 : response.filter((v) => v.uid === one._id),
                comments: comments === null || comments === void 0 ? void 0 : comments.filter((v) => v.to === one._id),
            };
            if (isSave)
                await this.app.service('session-takeaway-snapshot').create(post, params);
            logger_1.default.info('create takeaway snapshot', one._id, one.name, post.response);
        }
        rs.questionList = questionList;
        return rs;
    }
    // 自动计算出课堂统计数据
    stats(rs) {
        const stats = [];
        rs.stats = [];
        if (Acan.isEmpty(rs.questions))
            return;
        if (rs.type === 'videoSession') {
            for (const question of rs.questions) {
                const options = {};
                const users = {};
                for (const op of question.options) {
                    options[op._id] = 0;
                }
                for (const response of rs.response) {
                    if (response.type === 'choice') {
                        users[response.uid] = 1;
                        for (const answer of response.answer) {
                            if (answer && options.hasOwnProperty(answer)) {
                                options[answer] += 1;
                            }
                        }
                    }
                }
                stats.push({ page: question.page, question: question._id, answer: Object.keys(users).length, options: Object.values(options) });
            }
        }
        else {
            for (const page of rs.pages) {
                for (const question of rs.questions) {
                    if (question.page !== page._id)
                        continue;
                    const options = {};
                    const users = {};
                    for (const op of question.options) {
                        options[op._id] = 0;
                    }
                    for (const response of rs.response) {
                        if (response.page !== page._id)
                            continue;
                        if (response.type === 'choice') {
                            users[response.uid] = 1;
                            for (const answer of response.answer) {
                                options[answer] += 1;
                            }
                        }
                    }
                    stats.push({ page: page._id, question: question._id, answer: Object.keys(users).length, options: Object.values(options) });
                }
            }
        }
        rs.stats = stats;
    }
    async getStats({ _id, sid }) {
        const doc = Acan.clone(await this.get(_id || sid));
        this.stats(doc);
        return doc.stats;
    }
}
exports.SessionSnapshot = SessionSnapshot;
