"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Skills = void 0;
const feathers_mongoose_1 = require("feathers-mongoose");
class Skills extends feathers_mongoose_1.Service {
    //eslint-disable-next-line @typescript-eslint/no-unused-vars
    constructor(options, app) {
        super(options);
        this.app = app;
        this.publishKeys = ['standard', 'standardLevel'];
        this.selectList = ['uid', 'name', 'subtitle', 'curriculum', 'count', 'source', 'code', 'grade', 'standardLevel', 'publish', 'del', 'updatedAt', 'createdAt'];
    }
    async setSnapshot(_id, data) {
        const doc = Acan.clone(await this.Model.findById(_id));
        if (!Acan.isEmpty(doc.standard)) {
            data.publish = ['standard'];
            data.snapshot = {
                _id: doc._id,
                standard: doc.standard,
                standardLevel: doc.standardLevel,
            };
        }
        else {
            data.publish = [];
            data.snapshot = { _id: doc._id, standard: [], standardLevel: [] };
        }
    }
}
exports.Skills = Skills;
