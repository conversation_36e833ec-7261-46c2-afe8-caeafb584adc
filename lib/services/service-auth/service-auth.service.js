"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const service_auth_class_1 = require("./service-auth.class");
const service_auth_model_1 = __importDefault(require("../../models/service-auth.model"));
const service_auth_hooks_1 = __importDefault(require("./service-auth.hooks"));
function default_1(app) {
    const options = {
        Model: (0, service_auth_model_1.default)(app),
        whitelist: ['$exists', '$all', '$extSelect'],
        paginate: app.get('paginate'),
    };
    // Initialize our service with any options it requires
    app.use('/service-auth', new service_auth_class_1.ServiceAuth(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('service-auth');
    service.hooks(service_auth_hooks_1.default);
}
exports.default = default_1;
