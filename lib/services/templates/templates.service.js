"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const templates_class_1 = require("./templates.class");
const templates_model_1 = __importDefault(require("../../models/templates.model"));
const templates_hooks_1 = __importDefault(require("./templates.hooks"));
function default_1(app) {
    const options = {
        Model: (0, templates_model_1.default)(app),
        paginate: app.get('paginate')
    };
    // Initialize our service with any options it requires
    app.use('/templates', new templates_class_1.Templates(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('templates');
    service.hooks(templates_hooks_1.default);
}
exports.default = default_1;
