"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const template_class_1 = require("./template.class");
const template_model_1 = __importDefault(require("../../models/template.model"));
const template_hooks_1 = __importDefault(require("./template.hooks"));
function default_1(app) {
    const options = {
        Model: (0, template_model_1.default)(app),
        whitelist: ['$regex', '$options', '$search'],
        paginate: app.get('paginate'),
    };
    // Initialize our service with any options it requires
    app.use('/template', new template_class_1.Template(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('template');
    service.hooks(template_hooks_1.default);
}
exports.default = default_1;
