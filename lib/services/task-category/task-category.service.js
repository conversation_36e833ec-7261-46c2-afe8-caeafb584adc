"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const task_category_class_1 = require("./task-category.class");
const task_category_model_1 = __importDefault(require("../../models/task-category.model"));
const task_category_hooks_1 = __importDefault(require("./task-category.hooks"));
function default_1(app) {
    const options = {
        Model: (0, task_category_model_1.default)(app),
        paginate: app.get('paginate')
    };
    // Initialize our service with any options it requires
    app.use('/task-category', new task_category_class_1.TaskCategory(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('task-category');
    service.hooks(task_category_hooks_1.default);
}
exports.default = default_1;
