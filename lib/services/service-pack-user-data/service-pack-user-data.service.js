"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const service_pack_user_data_class_1 = require("./service-pack-user-data.class");
const service_pack_user_data_model_1 = __importDefault(require("../../models/service-pack-user-data.model"));
const service_pack_user_data_hooks_1 = __importDefault(require("./service-pack-user-data.hooks"));
function default_1(app) {
    const options = {
        Model: (0, service_pack_user_data_model_1.default)(app),
        multi: ['create'],
        paginate: app.get('paginate'),
    };
    // Initialize our service with any options it requires
    app.use('/service-pack-user-data', new service_pack_user_data_class_1.ServicePackUserData(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('service-pack-user-data');
    service.hooks(service_pack_user_data_hooks_1.default);
}
exports.default = default_1;
