import {HookContext} from '@feathersjs/feathers'
import logger from './logger'
const {NotFound, GeneralError, BadRequest} = require('@feathersjs/errors')

const hook = {
  async toClass(d: HookContext): Promise<any> {
    const method = (d.params.method = `${d.method}${(d.id + '').toFirstUpperCase()}`)
    if (d.service[method]) return (d.result = await d.service[method](['find', 'get'].includes(d.method) ? d.params.query : d.data, d.params)), d
  },
  classExist(d: HookContext) {
    const method = (d.params.method = `${d.method}${(d.id + '').toFirstUpperCase()}`)
    return !!d.service[method]
  },
  // 限制 find 的数据
  selectList(d: HookContext) {
    const query = d.params.query ?? {}
    if (!query.$select) query.$select = d.service.selectList.join(' ')
    else if (Array.isArray(query.$select)) query.$select = query.$select.join(' ')
    if (query.$extSelect) query.$select += ' ' + query.$extSelect.join(' ')
  },
  async find(Model: any, query: any) {
    const rs = {total: 0, limit: query.$limit || 10, skip: query.$skip || 0, data: []}
    const q = Model.find(query).limit(rs.limit).skip(rs.skip)
    if (query.$select) q.select(query.$select)
    rs.total = await Model.count(query)
    rs.data = await q.then()
    return rs
  },
  sysQuery(key = 'uid', onlySelf = true, managerRoles: string[] = []) {
    // 限制只能操作自己的数据，管理员除外
    return (d: HookContext) => {
      const query: any = d.params.query || {}
      // 管理后台 非管理员禁止访问
      if (query.$sys) {
        if (!this.roleHas(['sys', 'admin'])(d) && !this.managerRoleHas(managerRoles)(d)) return (d.result = null), d
        delete query.$sys
      } else {
        // 只能看自己的数据
        if (onlySelf || (!query[key] && !onlySelf)) query[key] = d.params.user?._id
      }
      d.params.query = query
    }
  },
  userQuery(key = 'uid') {
    // 按用户邮箱/手机/classcipeId查询
    return async (d: HookContext) => {
      let {userField, userFieldType, [key]: uid} = d.params.query || {}
      if (userField && userFieldType && !uid) {
        if (userFieldType === 'classcipeId') {
          userFieldType = 'email'
          userField = userField + '@classcipe.com'
        }
        let user = await d.app.service('users').Model.findOne({[userFieldType]: userField})
        if (!user) {
          d.result = {total: 0, skip: 0, data: [], limit: 0}
          return
        }
        d.params.query = {...d.params.query, [key]: user._id.toString()}
        delete d.params.query.userField
        delete d.params.query.userFieldType
      }
    }
  },
  roleFilter(arr: String[]) {
    // 过滤用户权限
    return (d: HookContext) => {
      if (d.params.inside) return Promise.resolve(d) // 允许内部调用
      const roles = d.params.user?.roles ?? []
      for (const role of arr) {
        if (roles.includes(role)) return Promise.resolve(d)
      }
      return Promise.reject(new GeneralError(new Error('Roles filter')))
    }
  },
  roleHas(arr: String[]) {
    // 权限存在判断
    return (d: any) => {
      const roles = d.params.user?.roles ?? []
      for (const role of arr) {
        if (roles.includes(role)) return true
      }
      return false
    }
  },
  managerRoleHas(arr: String[]) {
    // 管理权限存在判断
    return (d: any) => {
      const managerRoles = d.params.user?.managerRoles ?? []
      for (const role of arr) {
        if (managerRoles.includes(role)) return true
      }
      return false
    }
  },
  managerRoleFilter(arr: String[]) {
    // 过滤用户管理权限
    return (d: HookContext) => {
      if (d.params.inside) return Promise.resolve(d) // 允许内部调用
      const managerRoles = d.params.user?.managerRoles ?? []
      for (const role of arr) {
        if (managerRoles.includes(role)) return Promise.resolve(d)
      }
      return Promise.reject(new GeneralError(new Error('Manager Roles filter')))
    }
  },
  disable(d: HookContext) {
    d.result = null
  },
  selectLib(arr: String[], libKeys: String[] = []) {
    const lrr = arr.map((v: String) => (!Acan.isEmpty(libKeys) && libKeys.includes(v) ? 'snapshot.' + v : v))
    if (!arr.includes('_id')) lrr.push('snapshot._id')
    return lrr
  },
  resultLibOne(one: any, snapKeys: any = []) {
    for (const key of snapKeys) delete one[key]
    if (Acan.isEmpty(one.snapshot)) return one
    Object.assign(one, {...one.snapshot})
    delete one.snapshot
    return one
  },
  resultLib(data: any, snapKeys: any = []) {
    return data
      .map((v: any) => {
        return this.resultLibOne(v, snapKeys)
      })
      .filter((v: any) => !!v)
  },
  // 日期范围查询
  queryDate(key: any) {
    return (d: HookContext) => {
      const query = d.params.query ?? {}
      if (!query.dateRange) return d
      const [start, end, zone = 0] = query.dateRange
      delete query.dateRange
      query[key] = {$gte: utcDate(`${start}`, zone), $lte: utcDate(`${end}`, zone)}
    }
  },
  async extUserList(d: HookContext) {
    for (const o of d.result.data) {
      await hook.extUser(o)(d)
    }
  },
  extUser(one: any, key = 'uid') {
    return async (d: HookContext) => {
      if (!one[key]) return
      one.user = await d.app.service('users').uidToInfo(one.uid)
      if (one.school) {
        const rs = await d.app.service('school-user').getInfo({email: one.user.email, school: one.school})
        one.user = rs || one.user
      }
    }
  },
  disallow(context: HookContext) {
    throw new Error('This method is not allowed.')
  },
  disallowExternal(context: HookContext) {
    if (context.params.provider) {
      throw new Error('This method is not allowed from external sources.')
    }
    return context
  },
}

export default hook
