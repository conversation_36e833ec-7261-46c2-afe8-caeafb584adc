"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const point_log_class_1 = require("./point-log.class");
const point_log_model_1 = __importDefault(require("../../models/point-log.model"));
const point_log_hooks_1 = __importDefault(require("./point-log.hooks"));
function default_1(app) {
    const options = {
        Model: (0, point_log_model_1.default)(app),
        whitelist: ['$regex', '$options', '$search'],
        paginate: app.get('paginate'),
    };
    // Initialize our service with any options it requires
    app.use('/point-log', new point_log_class_1.PointLog(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('point-log');
    service.hooks(point_log_hooks_1.default);
}
exports.default = default_1;
