"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const authentication = __importStar(require("@feathersjs/authentication"));
const hook_1 = __importDefault(require("../../hook"));
// Don't remove this comment. It's needed to format import lines nicely.
const { authenticate } = authentication.hooks;
exports.default = {
    before: {
        all: [authenticate('jwt')],
        find: [],
        get: [hook_1.default.toClass],
        create: [
            async (d) => {
                var _a;
                d.data.uid = (_a = d.params.user) === null || _a === void 0 ? void 0 : _a._id;
                if (Acan.isEmpty(d.data.to))
                    delete d.data.to;
                if (d.data.visible && d.data.visible == 'private') {
                    const unit = await d.app.service('unit').Model.findOne({ _id: d.data.unit }).select('uid name').lean();
                    const recipient = await d.app.service('users').uidToInfo(unit.uid);
                    const user = await d.app.service('users').uidToInfo(d.data.uid);
                    d.app.service('notice-tpl').send('privatecommentonreflection', { _id: recipient._id, email: recipient.email }, {
                        username: user.nickname,
                        unit_name: unit.name,
                        url: `${SiteUrl}/v2/reflect/${d.data.unit}`,
                    });
                }
            },
        ],
        update: [hook_1.default.disable],
        patch: [
            (d) => {
                if (Acan.isEmpty(d.data.to))
                    delete d.data.to;
            },
        ],
        remove: [],
    },
    after: {
        all: [],
        find: [
            async (d) => {
                await d.service.extData(d.result.data);
            },
        ],
        get: [
            async (d) => {
                if (hook_1.default.classExist(d))
                    return d;
                await d.service.extData(d.result);
            },
        ],
        create: [
            async (d) => {
                await d.service.extData(d.result);
            },
        ],
        update: [],
        patch: [],
        remove: [],
    },
    error: {
        all: [],
        find: [],
        get: [],
        create: [],
        update: [],
        patch: [],
        remove: [],
    },
};
