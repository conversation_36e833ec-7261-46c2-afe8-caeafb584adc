"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const authentication = __importStar(require("@feathersjs/authentication"));
const authentication_local_1 = require("@feathersjs/authentication-local");
// Don't remove this comment. It's needed to format import lines nicely.
const { hashPassword, protect } = authentication_local_1.hooks;
const { authenticate } = authentication.hooks;
const search = require('feathers-mongodb-fuzzy-search');
const users_mod_1 = __importDefault(require("./users.mod"));
const app_1 = __importDefault(require("../../app"));
const hook_1 = __importDefault(require("../../hook"));
exports.default = {
    before: {
        all: [],
        find: [
            (d) => {
                if (d.params.authenticated)
                    return d;
                else
                    return authenticate('jwt')(d);
            },
            (d) => {
                const query = d.params.query || {};
                if (d.params.authenticated && query.$limit === 1)
                    return d; // 登录认证的内部调用跳过
                // 普通用户限制返回内容
                if (!hook_1.default.roleHas(['sys', 'admin'])(d))
                    query.$select = d.service.selectList;
            },
            search({
                fields: ['nickname', 'email'],
            }),
        ],
        get: [
            hook_1.default.toClass,
            (d) => {
                if (hook_1.default.classExist(d))
                    return;
                if (d.id && users_mod_1.default[d.id])
                    return users_mod_1.default[d.id](d);
                else if (/^\d+$/.test(d.id + ''))
                    return users_mod_1.default.get(d);
                else
                    return authenticate('jwt')(d);
            },
        ],
        create: [
            hashPassword('password'),
            async (d) => {
                const { roles } = d.data;
                if (roles.includes('sys') || roles.includes('admin'))
                    return Promise.reject(new Error('Error roles'));
                if (!roles || roles.length !== 1 || !['teacher', 'student'].includes(roles[0]))
                    return Promise.reject(new Error('roles error'));
                await d.service.checkCaptcha(d.data);
                d.data.ip = d.params.Aip;
                d.data.inviteCode = Math.round(Date.now() / 1000).toString(36) + Acan.random(1000, 9999).toString(36);
            },
        ],
        update: [authenticate('jwt'), hook_1.default.disable],
        patch: [
            hashPassword('password'),
            hook_1.default.toClass,
            async (d) => {
                var _a;
                if (hook_1.default.classExist(d))
                    return;
                await authenticate('jwt')(d);
                await d.service.checkCaptcha(d.data); // 存在验证码的时候，进行验证, 验证后删除缓存
                const { _id } = (_a = d.params.user) !== null && _a !== void 0 ? _a : {};
                const redisClient = d.app.get('redis');
                if (d.id !== _id)
                    hook_1.default.roleFilter(['sys'])(d); // 普通用户不能修改他人的信息
                redisClient.HDEL('_id:info', d.id);
                if (d.data.name)
                    d.data.nickname = d.data.name.join(' ');
            },
        ],
        remove: [authenticate('jwt'), hook_1.default.roleFilter(['sys', 'admin'])],
    },
    after: {
        all: [protect('password')],
        find: [],
        get: [],
        create: [
            (d) => {
                d.service.mailReg(d.result, d.params);
                const { roles, inviter } = d.data;
                const { _id } = d.result;
                if (inviter) {
                    app_1.default.service('point-log').getAddLog({
                        inviter: inviter,
                        tab: 'earn',
                        source: 'reward',
                        category: 'invite',
                        businessId: _id,
                        snapshot: d.result,
                    });
                }
            },
        ],
        update: [],
        patch: [
            (d) => {
                let { _id } = d.result;
                _id = _id.toString();
                if (Acan.isObjectId(_id))
                    d.app.get('redis').HDEL('_id:info', _id);
            },
        ],
        remove: [
            async (d) => {
                const { email, google } = d.result;
                if (google) {
                    const redis = d.app.get('redis');
                    redis.del(`googleAuth:${google}`);
                    redis.del(`googleRT:${google}`);
                    await app_1.default.service('user-token').Model.deleteOne({ sub: google });
                }
            },
        ],
    },
    error: {
        all: [],
        find: [],
        get: [],
        create: [],
        update: [],
        patch: [],
        remove: [],
    },
};
