"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SessionTakeaway = void 0;
const feathers_mongoose_1 = require("feathers-mongoose");
class SessionTakeaway extends feathers_mongoose_1.Service {
    //eslint-disable-next-line @typescript-eslint/no-unused-vars
    constructor(options, app) {
        super(options);
        this.app = app;
    }
}
exports.SessionTakeaway = SessionTakeaway;
