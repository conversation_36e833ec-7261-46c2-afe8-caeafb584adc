"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const zoom_meet_class_1 = require("./zoom-meet.class");
const zoom_meet_hooks_1 = __importDefault(require("./zoom-meet.hooks"));
function default_1(app) {
    const options = {
        paginate: app.get('paginate')
    };
    // Initialize our service with any options it requires
    app.use('/zoom-meet', new zoom_meet_class_1.ZoomMeet(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('zoom-meet');
    service.hooks(zoom_meet_hooks_1.default);
}
exports.default = default_1;
