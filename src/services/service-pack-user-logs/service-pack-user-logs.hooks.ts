import {HooksObject} from '@feathersjs/feathers'
import * as authentication from '@feathersjs/authentication'
// Don't remove this comment. It's needed to format import lines nicely.

const {authenticate} = authentication.hooks

export default {
  before: {
    all: [authenticate('jwt')],
    find: [],
    get: [],
    create: [
      /** @transactional | `params` 可能包含一个 Mongoose session，所有数据库调用以及新添加的嵌套函数必须使用该 session。*/
    ],
    update: [],
    patch: [],
    remove: [],
  },

  after: {
    all: [],
    find: [],
    get: [],
    create: [
      /** @transactional | `params` 可能包含一个 Mongoose session，所有数据库调用以及新添加的嵌套函数必须使用该 session。*/
    ],
    update: [],
    patch: [],
    remove: [],
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: [],
  },
}
