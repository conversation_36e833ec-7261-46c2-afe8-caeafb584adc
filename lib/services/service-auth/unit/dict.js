"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getTopicData = exports.OverseasStudyMap = exports.getVerificationKey = exports.sysCurriculumKeys = exports.sortBySysCurriculum = exports.VerificationList = exports.getTitleLabel = exports.VerificationMap = exports.sysCurriculumMap = void 0;
const pubCurriculumList = {
    'ib-pyp': 'IB-PYP',
    'ib-myp': 'IB-MYP',
    'ib-dp': 'IB-DP',
    'cam-prim': 'Cambridge-Primary',
    'cam-low': 'Cambridge Lower secondary',
    'cam-as': 'Cambridge AS & A level',
    us: 'US curriculum',
    au: 'AU curriculum',
    nz: 'NZ National',
    cbse: 'CBSE',
    igcse: 'IGCSE',
    others: 'Others',
};
const EducatorGrades = ['Introductory level', 'Intermediate level', 'Advanced level'];
const CurriculumGrades = {
    au: [],
    us: [],
    'ib-pyp': [],
    'ib-myp': [],
    'ib-dp': [],
    cbse: [],
    'cam-prim': [],
    'cam-low': [],
    'cam-as': [],
    igcse: [],
    nz: [],
    pd: EducatorGrades,
};
const VerificationList = [
    { label: 'Premium workshop', value: 'workshop' },
    { label: 'Premium content', value: 'content' },
    { label: 'Academic', value: 'mentoring:academic' },
    // topic level 2
    { label: 'Essay', value: 'mentoring:essay' },
    // topic level 1 ?
    { label: 'Overseas study', value: 'mentoring:overseasStudy' },
    // topic level 1
    { label: 'Teacher training', value: 'mentoring:teacherTraining' },
    // topic level 1
    { label: 'STEAM', value: 'mentoring:steam' },
    // topic level 2
    { label: 'Teacher training-subject', value: 'mentoring:teacherTrainingSubject' },
    // topic level 1
    { label: 'Academic planning', value: 'mentoring:academicPlanning' },
    // topic level 1
    { label: 'Application and visa assistance', value: 'mentoring:personalStatement' },
    // topic level 1
    { label: 'Interest & Career practice', value: 'mentoring:interest' },
    // old
    // {label: 'Accredited content', value: 'teaching'},
    // {label: 'Substitute academic', value: 'substituteAcademic'},
    // {label: 'Substitute service', value: 'substituteService'},
    // {label: 'Correcting service', value: 'correcting'},
];
exports.VerificationList = VerificationList;
const OverseasStudyList = [
    { label: 'Australia', value: 'au' },
    { label: 'Canada', value: 'ca' },
    { label: 'New Zealand', value: 'nz' },
    { label: 'UK', value: 'uk' },
    { label: 'US', value: 'us' },
];
const OverseasStudyMap = OverseasStudyList.reduce((acc, cur) => {
    if (cur === null || cur === void 0 ? void 0 : cur.value)
        acc[cur.value] = cur;
    return acc;
}, {});
exports.OverseasStudyMap = OverseasStudyMap;
const array = Object.entries(CurriculumGrades);
let result = [];
array.forEach(([key, value]) => {
    if (pubCurriculumList[key])
        result.push({ value: key, label: pubCurriculumList[key] });
});
let curriculumList = [...result, { value: 'pd', label: 'Service' }];
let sysCurriculumList = [...curriculumList];
const sysCurriculumMap = sysCurriculumList.reduce((acc, cur) => {
    if (cur === null || cur === void 0 ? void 0 : cur.value)
        acc[cur.value] = cur;
    return acc;
}, {});
exports.sysCurriculumMap = sysCurriculumMap;
const VerificationMap = VerificationList.reduce((acc, cur) => {
    if (cur === null || cur === void 0 ? void 0 : cur.value)
        acc[cur.value] = cur;
    return acc;
}, {});
exports.VerificationMap = VerificationMap;
function getTitleLabel(key) {
    var _a, _b, _c, _d;
    if (key === 'workshop')
        return `${(_a = VerificationMap === null || VerificationMap === void 0 ? void 0 : VerificationMap[key]) === null || _a === void 0 ? void 0 : _a.label}`;
    if (key === 'content')
        return `${(_b = VerificationMap === null || VerificationMap === void 0 ? void 0 : VerificationMap[key]) === null || _b === void 0 ? void 0 : _b.label}`;
    const isMentoring = key.includes('mentoring:');
    let label = '';
    if (isMentoring)
        label = `${(_c = VerificationMap === null || VerificationMap === void 0 ? void 0 : VerificationMap[key]) === null || _c === void 0 ? void 0 : _c.label}`;
    else
        label = `${(_d = VerificationMap === null || VerificationMap === void 0 ? void 0 : VerificationMap[key]) === null || _d === void 0 ? void 0 : _d.label}`;
    return label;
}
exports.getTitleLabel = getTitleLabel;
function sortBySysCurriculum(list) {
    const result = [];
    sysCurriculumKeys.forEach((code) => {
        const target = list.find((e) => (e === null || e === void 0 ? void 0 : e.code) === code || (e === null || e === void 0 ? void 0 : e.value) === code);
        if (target)
            result.push(target);
    });
    return result;
}
exports.sortBySysCurriculum = sortBySysCurriculum;
const sysCurriculumKeys = sysCurriculumList.map((e) => e.value);
exports.sysCurriculumKeys = sysCurriculumKeys;
function getVerificationKey(verification) {
    var _a;
    let key = (_a = verification === null || verification === void 0 ? void 0 : verification.type) !== null && _a !== void 0 ? _a : '';
    if (verification === null || verification === void 0 ? void 0 : verification.mentoringType)
        key += `:${verification.mentoringType}`;
    return key;
}
exports.getVerificationKey = getVerificationKey;
function getTopicData(list) {
    const keys = list.reduce((acc, cur) => {
        var _a, _b, _c, _d;
        const key = (_d = (_c = (_b = (_a = cur === null || cur === void 0 ? void 0 : cur.topic) === null || _a === void 0 ? void 0 : _a[0]) === null || _b === void 0 ? void 0 : _b.label) === null || _c === void 0 ? void 0 : _c[0]) !== null && _d !== void 0 ? _d : '';
        if (!acc.includes(key) && key)
            acc.push(key);
        return acc;
    }, []);
    const result = keys.map((e) => ({ label: e, child: [] }));
    list.forEach((e) => {
        var _a, _b, _c, _d;
        const key = (_d = (_c = (_b = (_a = e === null || e === void 0 ? void 0 : e.topic) === null || _a === void 0 ? void 0 : _a[0]) === null || _b === void 0 ? void 0 : _b.label) === null || _c === void 0 ? void 0 : _c[0]) !== null && _d !== void 0 ? _d : '';
        const target = result.find((i) => i.label === key);
        if (target === null || target === void 0 ? void 0 : target.child)
            target.child.push(e);
    });
    return result;
}
exports.getTopicData = getTopicData;
