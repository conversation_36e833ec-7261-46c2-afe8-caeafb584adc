"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PointLog = void 0;
const feathers_mongoose_1 = require("feathers-mongoose");
const { NotFound, GeneralError, BadRequest } = require('@feathersjs/errors');
class PointLog extends feathers_mongoose_1.Service {
    //eslint-disable-next-line @typescript-eslint/no-unused-vars
    constructor(options, app) {
        super(options);
        this.app = app;
    }
    // 统计
    async getCount({}, params) {
        var _a;
        const uid = (_a = params === null || params === void 0 ? void 0 : params.user) === null || _a === void 0 ? void 0 : _a._id;
        let expectedAmount = await this.Model.aggregate([
            { $match: { status: 0, tab: 'earn', uid } },
            {
                $group: {
                    _id: '$uid',
                    amount: { $sum: '$value' }, // 对 amount 字段求和
                },
            },
        ]);
        let categoryList = [
            'refund',
            'order',
            'self_study',
            'service',
            'points_purchase',
            'invite',
            'session',
            'service_substitute',
            'saas_tool_trail',
            'saas_tool_paid',
            'service_correct',
            'service_premium',
            'verify',
            'unit',
            'task',
        ];
        let countArr = {};
        for (let i = 0; i < categoryList.length; i++) {
            const item = categoryList[i];
            let count = await this.Model.count({ uid, category: item });
            countArr[item] = count;
        }
        let expectedCount = await this.Model.count({ uid, status: 0 });
        let ActualCount = await this.Model.count({ uid, status: 1 });
        return {
            expected_amount: expectedAmount[0].amount,
            ...countArr,
            expected_count: expectedCount,
            actual_count: ActualCount,
        };
        // await this.goodsFilter({buyer: params?.user?._id}, params.user)
        // let count = await this.Model.count({buyer: params.user?._id})
        // return {
        //   count,
        // }
    }
    /**
     * 增加log,同时更新用户积分
     * amount 积分 或者金额美分
     */
    async getAddLog({ uid, inviter, change, tab, source, category, categoryType, amount = 0, businessId = '', snapshot, isSchool = false, status = 1, }) {
        let user;
        if (uid) {
            if (isSchool) {
                user = await this.app.service('school-plan').Model.findOne({ _id: uid });
            }
            else {
                user = await this.app.service('users').Model.findOne({ _id: uid });
            }
        }
        else {
            user = await this.app.service('users').Model.findOne({ inviteCode: inviter });
        }
        let value = change;
        if (!change) {
            let resPoint = await this.app.service('point-setting').calcPoint({ type: { category }, amount, tab, isPoint: false });
            value = resPoint.point;
        }
        let total = user.point;
        if (status == 1) {
            total = Number((user.point + Number(value)).toFixed(0));
            if (isSchool) {
                await this.app.service('school-plan').Model.findByIdAndUpdate(user._id, { point: total });
            }
            else {
                await this.app.service('users').Model.findByIdAndUpdate(user._id, { point: total });
            }
        }
        return this.Model.updateOne({
            uid: user._id.toString(),
            tab,
            source,
            category,
            categoryType,
            businessId,
            isSchool,
        }, [
            {
                $set: {
                    uid: user._id.toString(),
                    tab,
                    source,
                    category,
                    categoryType,
                    value,
                    total,
                    businessId,
                    snapshot,
                    isSchool,
                    status,
                    expected: '$value',
                },
            },
        ], { upsert: true });
    }
    /**
     * 弃用 改为service('point-setting').calcPoint
     * 计算应获得的积分值
     * (0,1] => 1
     * 1以上向下取整
     * amount 积分 或者金额美分
     */
    // async getPointValue({tab, category, categoryType, amount}: any): Promise<any> {
    //   let query: any = {
    //     tab,
    //     category,
    //   }
    //   if (categoryType) query.categoryType = categoryType
    //   let setting: any = await this.app.service('point-setting').Model.findOne(query)
    //   if (!setting) return 0
    //   if (setting.mode === 'fixed') {
    //     return setting.value
    //   } else {
    //     let num = (setting.value / 100) * amount
    //     if (!num) return 0
    //     if (num <= 1) return 1
    //     return Math.floor(num)
    //   }
    // }
    // 增加积分佣金log添加
    async handleAddLog(log) {
        if (log.type == 'point') {
            await this.getAddLog(log);
        }
        else if (log.type == 'commission') {
            await this.app.service('commission-log').getAddLog(log);
        }
    }
}
exports.PointLog = PointLog;
