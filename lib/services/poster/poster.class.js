"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Poster = void 0;
const feathers_mongoose_1 = require("feathers-mongoose");
const { NotFound, GeneralError, BadRequest } = require('@feathersjs/errors');
const got = require('got');
class Poster extends feathers_mongoose_1.Service {
    //eslint-disable-next-line @typescript-eslint/no-unused-vars
    constructor(options, app) {
        super(options);
        this.app = app;
    }
    async getMergeImage({ image, imageCover, url }, params) {
        if (!image) {
            return Promise.reject(new GeneralError('image is required'));
        }
        const ubj = new URL(this.app.get('api3Url') + '/image/posterMerge');
        ubj.searchParams.set('image', image);
        ubj.searchParams.set('imageCover', imageCover || '');
        ubj.searchParams.set('url', url || '');
        const rs = (await got(ubj.href, { json: true })).body;
        return rs;
    }
}
exports.Poster = Poster;
