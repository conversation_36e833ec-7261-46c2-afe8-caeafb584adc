"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const service_booking_class_1 = require("./service-booking.class");
const service_booking_model_1 = __importDefault(require("../../models/service-booking.model"));
const service_booking_hooks_1 = __importDefault(require("./service-booking.hooks"));
function default_1(app) {
    const options = {
        Model: (0, service_booking_model_1.default)(app),
        whitelist: ['$regex', '$options', '$search', '$exists'],
        paginate: app.get('paginate'),
    };
    // Initialize our service with any options it requires
    app.use('/service-booking', new service_booking_class_1.ServiceBooking(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('service-booking');
    service.hooks(service_booking_hooks_1.default);
}
exports.default = default_1;
