"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServiceAuth = void 0;
const feathers_mongoose_1 = require("feathers-mongoose");
const errors_1 = require("@feathersjs/errors");
const dict_1 = require("./unit/dict"); // @ts-ignore
const hook_1 = __importDefault(require("../../hook"));
class ServiceAuth extends feathers_mongoose_1.Service {
    //eslint-disable-next-line @typescript-eslint/no-unused-vars
    constructor(options, app) {
        super(options);
        this.allowRoles = ['customer_service', 'customer_service_manager', 'academic_consultant', 'admin'];
        this.selectList = [
            'uid',
            'enable',
            'status',
            'type',
            'mentoringType',
            'serviceRoles',
            'countryCode',
            'curriculum',
            'subject',
            'gradeGroup',
            'grades',
            'topic',
            'desc',
            'createdAt',
            'updatedAt',
            'qualification',
            'feedback',
            'follower',
            'followedAt',
            'releasedAt',
            'schoolOfFollower',
            'tags',
            'importUsers',
            'unit',
            'unitSnapshot.name',
            'unitSnapshot.mode',
            'approval',
        ];
        this.app = app;
    }
    async getCountType(query, params) {
        return await this.Model.aggregate([
            { $match: {} },
            {
                $group: {
                    _id: {
                        type: '$type',
                        mentoringType: '$mentoringType',
                        status: '$status',
                    },
                    count: { $sum: 1 },
                },
            },
        ]);
    }
    // 统计认证老师数量，用于发布服务包看
    async getGroups(query, params) {
        return await this.groups(query);
    }
    // 统计认证老师数量，用于发布服务包看
    async groups(query, enableRs) {
        delete query.serviceRoles;
        const arr = await this.Model.aggregate([
            { $match: query },
            {
                $group: {
                    _id: {
                        countryCode: '$countryCode',
                        curriculum: '$curriculum',
                        subject: '$subject',
                        gradeGroup: '$gradeGroup',
                        serviceRoles: '$serviceRoles',
                        status: '$status',
                        enable: '$enable',
                        topic: '$topic._id',
                    },
                    count: { $sum: 1 },
                },
            },
        ]);
        const rs = {
            countryCode: {},
            curriculum: {},
            subject: {},
            gradeGroup: {},
            serviceRoles: {},
            topic: {},
        };
        for (const { _id, count } of arr) {
            for (const key of Object.keys(rs)) {
                if (_id[key]) {
                    if (!rs[key][_id[key]])
                        rs[key][_id[key]] = [];
                    rs[key][_id[key]].push({ ..._id, count });
                }
            }
        }
        rs.enableRs = enableRs;
        return rs;
    }
    // 统计认证课件数量
    // query: {type, curriculum, subject}
    async getGroupTopic(query) {
        delete query['topic._id'];
        query['unit._id'] = { $exists: true };
        query.status = 2;
        const arr = await this.Model.aggregate([
            { $match: query },
            {
                $group: {
                    _id: {
                        topic: '$topic._id',
                    },
                    count: { $sum: 1 },
                },
            },
        ]);
        const rs = {};
        for (const { _id, count } of arr) {
            for (const topic of _id.topic) {
                if (!rs[topic])
                    rs[topic] = count;
                else
                    rs[topic] += count;
            }
        }
        return rs;
    }
    // 统计认证老师数量，用于发布服务包看
    // async getStats(query: any, params: Params) {
    //   const arr: any = await this.Model.find(query).limit(1000)
    //   const rs: any = {
    //     countryCode: {},
    //     curriculum: {},
    //     subject: {},
    //     gradeGroup: {},
    //   }
    //   for (const one of arr) {
    //     for (const key of Object.keys(rs)) {
    //       if (Acan.isEmpty(one[key])) continue
    //       if (Array.isArray(one[key])) {
    //         for (const ck of one[key]) {
    //           if (!rs[key][ck]) rs[key][ck] = {}
    //           if (!rs[key][ck][one.status]) rs[key][ck][one.status] = 0
    //           rs[key][ck][one.status]++
    //         }
    //       } else {
    //         if (!rs[key][one[key]]) rs[key][one[key]] = {}
    //         if (!rs[key][one[key]][one.status]) rs[key][one[key]][one.status] = 0
    //         rs[key][one[key]][one.status]++
    //       }
    //     }
    //   }
    //   return rs
    // }
    // 后台用户和本人除外，限制ppt页数
    async extPagesLimit(o, params) {
        var _a, _b;
        if (((_a = params.user) === null || _a === void 0 ? void 0 : _a._id) === o.uid)
            return; // 本人不限制
        if (hook_1.default.roleHas(this.allowRoles)({ params }))
            return; // 后台用户不限制
        if (o.unitSnapshot && Acan.isEmpty((_b = o.unitSnapshot) === null || _b === void 0 ? void 0 : _b.link)) {
            const pageNum = o.unitSnapshot.pageNum;
            if (o.unitSnapshot.pages)
                o.unitSnapshot.pages.length = pageNum > 10 ? 3 : Math.ceil(pageNum * 0.3);
        }
        else {
            if (!o.linkSnapshot)
                return;
            for (const key in o.linkSnapshot) {
                const pageNum = o.linkSnapshot[key].pageNum;
                if (o.linkSnapshot[key].pages)
                    o.linkSnapshot[key].pages.length = pageNum > 10 ? 3 : Math.ceil(pageNum * 0.3);
            }
        }
    }
    async ext(o) {
        var _a, _b;
        if (o.uid)
            o.owner = await this.app.service('users').uidToInfo(o.uid);
        if ((_a = o.unitSnapshot) === null || _a === void 0 ? void 0 : _a.uid) {
            o.unitSnapshot.owner = await this.app.service('users').uidToInfo(o.unitSnapshot.uid);
        }
        if ((_b = o.approval) === null || _b === void 0 ? void 0 : _b.approver) {
            Object.assign(o.approval, await this.app.service('users').uidToInfo(o.approval.approver));
        }
    }
    // 获取单个老师的已认证数据
    getListByUid({ uid }) {
        return this.Model.find({ uid, status: 2 }).select(this.selectList);
    }
    async send(doc, tpl, params) {
        var _a, _b;
        const user = await this.app.service('users').uidToInfo(doc.uid);
        let name = await this.getAuthName(doc);
        let url = '';
        if (tpl === 'verificationrejected')
            url = `${SiteUrl}/v2/account/teacher/auth/edit/verification?dialogId=${doc._id}`;
        if (tpl === 'VerificationStatusChanged')
            url = `${SiteUrl}/v2/account/teacher/auth/introduction`;
        if (tpl === 'verificationapproved')
            url = `${SiteUrl}/v2/com/agreement/service_provider/verification`;
        if (tpl === 'ReminderOfInterview(Teachers)')
            url = `${SiteUrl}/v2/service/pack/${doc.interviewPack}`;
        if (tpl === 'Approval/RejectionOfPremiumContent')
            url = `${SiteUrl}/v2/account/teacher/auth/edit/verification?dialogId=${doc._id}`;
        if (tpl === 'SetTheServiceAvailabilityUnderTheCalendar')
            url = `${SiteUrl}/v2/account/teacher/auth/view/availability`;
        let result = 'Approved';
        if (doc.status == -1) {
            result = 'Rejected';
        }
        return await this.app.service('notice-tpl').mailto(tpl, user.email, {
            username: user.name.join(' '),
            name: name.join('-'),
            name_under: name.slice(0, name.length - 1).join('-'),
            url,
            reason: doc.reason,
            service_type: name[name.length - 1],
            result,
            unit_name: (_a = doc === null || doc === void 0 ? void 0 : doc.unit) === null || _a === void 0 ? void 0 : _a.name,
        }, (_b = params.user) === null || _b === void 0 ? void 0 : _b._id);
    }
    // 获取link下的快照
    async getUnitSnapshotLink(snapshot, linkSnapshot, params) {
        for (const group of snapshot.linkGroup) {
            for (const o of snapshot.link) {
                if (o.group !== group._id)
                    continue;
                if (linkSnapshot[o.id])
                    continue; // 去重
                const rs = await this.app.service('unit').snapshot({ _id: o.id }, params);
                if (!rs)
                    return Promise.reject(new errors_1.GeneralError(`Link content:${o.id} not found`));
                if (rs.isEdit)
                    return Promise.reject(new errors_1.GeneralError(`Link content:${rs.name} in editing`));
                if (!rs.filled)
                    return Promise.reject(new errors_1.GeneralError(`Link content:${rs.name} ${o.id} incomplete`));
                linkSnapshot[o.id] = rs;
                if (!Acan.isEmpty(rs.link) && !Acan.isEmpty(rs.linkGroup))
                    await this.getUnitSnapshotLink(rs, linkSnapshot, params);
            }
        }
    }
    // 生成整个 unit 快照
    async getUnitSnapshot({ _id, unit }, params) {
        const unitSnapshot = Acan.clone(await this.app.service('unit').snapshot({ _id: unit }, params));
        if (unitSnapshot.isEdit)
            return Promise.reject(new errors_1.GeneralError(`Unit:${unitSnapshot.name} in editing`));
        if (!unitSnapshot.filled)
            return Promise.reject(new errors_1.GeneralError(`Unit:${unitSnapshot.name} incomplete`));
        const linkSnapshot = {};
        await this.getUnitSnapshotLink(unitSnapshot, linkSnapshot, params);
        const $set = { unitSnapshot, linkSnapshot };
        // 课件认证 计算出价格
        if (['task', 'pdTask'].includes(unitSnapshot.mode)) {
            $set['unit.price'] = unitSnapshot.questions.length * 20;
        }
        return await this.Model.updateOne({ _id }, { $set });
    }
    // 查询已经认证的精品课数据
    async getUnit({}, params) {
        var _a;
        const query = (_a = params.query) !== null && _a !== void 0 ? _a : {};
        query['unitSnapshot._id'] = { $ne: null };
        const unitSelect = this.app.service('unit').selectList.map((v) => 'unitSnapshot.' + v);
        query.$select = [...this.selectList, ...unitSelect, 'unitSnapshot._id'];
        return await hook_1.default.find(this.Model, query);
    }
    async getAuthName(doc) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v, _w, _x, _y, _z, _0, _1, _2, _3, _4, _5, _6, _7, _8, _9, _10, _11, _12, _13, _14, _15, _16, _17, _18, _19, _20, _21, _22, _23, _24, _25, _26, _27, _28, _29, _30, _31, _32, _33, _34, _35, _36, _37, _38, _39, _40, _41, _42;
        let key = `${doc.type}${doc.mentoringType ? ':' + doc.mentoringType : ''}`;
        const subject = await this.app.service('subjects').find({ query: { uid: '1', $limit: 500, isLib: true } });
        let sysList = (subject === null || subject === void 0 ? void 0 : subject.data) || [];
        const sysSubjectMap = sysList.reduce((acc, cur) => {
            if (cur === null || cur === void 0 ? void 0 : cur._id)
                acc[cur._id] = cur;
            return acc;
        }, {});
        const keys = dict_1.VerificationList.map((e) => e.value);
        const result = {};
        const callbacks = [];
        for (const key of keys) {
            callbacks.push(this.getTeacherVerificationConfig(`Service:${key}`));
        }
        const resArray = await Promise.all(callbacks);
        keys.forEach((key, i) => {
            if (resArray[i])
                result[key] = resArray[i];
        });
        let allTeacherVerificationConfig = result;
        const sysSubjectMapByCurriculum = () => {
            const result = {};
            dict_1.sysCurriculumKeys.forEach((code) => {
                var _a;
                const filtered = (_a = sysList.filter((e) => !(e === null || e === void 0 ? void 0 : e.del)).filter((e) => { var _a; return (_a = e === null || e === void 0 ? void 0 : e.curriculum) === null || _a === void 0 ? void 0 : _a.includes(code); })) !== null && _a !== void 0 ? _a : [];
                result[code] = filtered;
            });
            return result;
        };
        const auth = await this.app.service('service-auth').Model.find();
        let list = auth !== null && auth !== void 0 ? auth : [];
        list = list.sort((a, b) => +new Date(b.updatedAt) - +new Date(a.updatedAt));
        let userVerificationMap = {};
        for (const e of list) {
            if (!(e === null || e === void 0 ? void 0 : e.type))
                continue;
            let key = (0, dict_1.getVerificationKey)(e);
            if (!userVerificationMap[key])
                userVerificationMap[key] = [];
            userVerificationMap[key].push(e);
        }
        let name = [];
        let label = (0, dict_1.getTitleLabel)(key);
        name.unshift(label);
        if (key == 'workshop') {
            if (doc.curriculum !== 'pd') {
                name.unshift('Academic');
                name.unshift((_a = dict_1.sysCurriculumMap === null || dict_1.sysCurriculumMap === void 0 ? void 0 : dict_1.sysCurriculumMap[doc.curriculum]) === null || _a === void 0 ? void 0 : _a.label);
            }
            if (doc.curriculum === 'pd') {
                name.unshift((_b = dict_1.sysCurriculumMap['pd']) === null || _b === void 0 ? void 0 : _b.label);
                name.unshift((_c = sysSubjectMap === null || sysSubjectMap === void 0 ? void 0 : sysSubjectMap[doc.subject]) === null || _c === void 0 ? void 0 : _c.name);
            }
        }
        if (key == 'correcting') {
            name.unshift((_d = dict_1.sysCurriculumMap[doc.curriculum]) === null || _d === void 0 ? void 0 : _d.label);
            name.unshift((_e = sysSubjectMap === null || sysSubjectMap === void 0 ? void 0 : sysSubjectMap[doc.subject]) === null || _e === void 0 ? void 0 : _e.name);
        }
        if (key == 'substituteAcademic') {
            name.unshift((_f = dict_1.sysCurriculumMap[doc.curriculum]) === null || _f === void 0 ? void 0 : _f.label);
            name.unshift((_g = sysSubjectMap === null || sysSubjectMap === void 0 ? void 0 : sysSubjectMap[doc.subject]) === null || _g === void 0 ? void 0 : _g.name);
        }
        if (key == 'substituteService') {
            let cur = sysSubjectMapByCurriculum()['pd'].find((e) => e._id == doc.subject);
            let curVerification = userVerificationMap[key].find((e) => e.subject == doc.subject);
            if (cur.name.toLowerCase() === 'teacher training') {
                name.unshift(cur.name);
                name.unshift((_k = (_j = (_h = curVerification.topic) === null || _h === void 0 ? void 0 : _h[0]) === null || _j === void 0 ? void 0 : _j.label) === null || _k === void 0 ? void 0 : _k[((_o = (_m = (_l = curVerification.topic) === null || _l === void 0 ? void 0 : _l[0]) === null || _m === void 0 ? void 0 : _m.label) === null || _o === void 0 ? void 0 : _o.length) - 1]);
            }
            if (cur.name.toLowerCase() === 'overseas study') {
                name.unshift(cur.name);
                name.unshift((_p = dict_1.OverseasStudyMap[doc.countryCode]) === null || _p === void 0 ? void 0 : _p.label);
            }
            if (cur.name.toLowerCase() === 'essay') {
                name.unshift(cur.name);
                name.unshift((_s = (_r = (_q = curVerification.topic) === null || _q === void 0 ? void 0 : _q[0]) === null || _r === void 0 ? void 0 : _r.label) === null || _s === void 0 ? void 0 : _s[((_v = (_u = (_t = curVerification.topic) === null || _t === void 0 ? void 0 : _t[0]) === null || _u === void 0 ? void 0 : _u.label) === null || _v === void 0 ? void 0 : _v.length) - 1]);
            }
            if (cur.name.toLowerCase() === 'steam') {
                name.unshift(cur.name);
                name.unshift((_y = (_x = (_w = curVerification.topic) === null || _w === void 0 ? void 0 : _w[0]) === null || _x === void 0 ? void 0 : _x.label) === null || _y === void 0 ? void 0 : _y[((_1 = (_0 = (_z = curVerification.topic) === null || _z === void 0 ? void 0 : _z[0]) === null || _0 === void 0 ? void 0 : _0.label) === null || _1 === void 0 ? void 0 : _1.length) - 1]);
            }
        }
        if (key == 'mentoring:academic') {
            // name.push(getTitleLabel(key))
            name.unshift((_2 = dict_1.sysCurriculumMap[doc.curriculum]) === null || _2 === void 0 ? void 0 : _2.label);
            name.unshift((_3 = sysSubjectMap === null || sysSubjectMap === void 0 ? void 0 : sysSubjectMap[doc.subject]) === null || _3 === void 0 ? void 0 : _3.name);
        }
        if (key == 'mentoring:overseasStudy') {
            // name.push(getTitleLabel(key))
            name.unshift((_4 = dict_1.OverseasStudyMap === null || dict_1.OverseasStudyMap === void 0 ? void 0 : dict_1.OverseasStudyMap[doc.countryCode]) === null || _4 === void 0 ? void 0 : _4.label);
        }
        if (key == 'mentoring:essay') {
            let cur = sysSubjectMapByCurriculum()['pd'].find((e) => e._id == doc.subject);
            let curVerification = userVerificationMap[key].find((e) => e.subject == doc.subject);
            // name.push(getTitleLabel(key))
            name.unshift((_7 = (_6 = (_5 = curVerification.topic) === null || _5 === void 0 ? void 0 : _5[0]) === null || _6 === void 0 ? void 0 : _6.label) === null || _7 === void 0 ? void 0 : _7[((_10 = (_9 = (_8 = curVerification.topic) === null || _8 === void 0 ? void 0 : _8[0]) === null || _9 === void 0 ? void 0 : _9.label) === null || _10 === void 0 ? void 0 : _10.length) - 1]);
        }
        if (key == 'mentoring:teacherTraining') {
            let cur = sysSubjectMapByCurriculum()['pd'].find((e) => e._id == doc.subject);
            let curVerification = userVerificationMap[key].find((e) => e.subject == doc.subject);
            // name.push(getTitleLabel(key))
            name.unshift((_13 = (_12 = (_11 = curVerification.topic) === null || _11 === void 0 ? void 0 : _11[0]) === null || _12 === void 0 ? void 0 : _12.label) === null || _13 === void 0 ? void 0 : _13[((_16 = (_15 = (_14 = curVerification.topic) === null || _14 === void 0 ? void 0 : _14[0]) === null || _15 === void 0 ? void 0 : _15.label) === null || _16 === void 0 ? void 0 : _16.length) - 1]);
        }
        if (key == 'mentoring:steam') {
            let curVerification = userVerificationMap[key].find((e) => e.subject == doc.subject);
            name.unshift((_19 = (_18 = (_17 = curVerification.topic) === null || _17 === void 0 ? void 0 : _17[0]) === null || _18 === void 0 ? void 0 : _18.label) === null || _19 === void 0 ? void 0 : _19[((_22 = (_21 = (_20 = curVerification.topic) === null || _20 === void 0 ? void 0 : _20[0]) === null || _21 === void 0 ? void 0 : _21.label) === null || _22 === void 0 ? void 0 : _22.length) - 1]);
        }
        if (key == 'mentoring:teacherTrainingSubject') {
            let curVerification = userVerificationMap[key].find((e) => e.subject == doc.subject);
            name.unshift(...(_24 = (_23 = curVerification.topic) === null || _23 === void 0 ? void 0 : _23[0]) === null || _24 === void 0 ? void 0 : _24.label.reverse());
        }
        if (key == 'mentoring:academicPlanning') {
            let curVerification = userVerificationMap[key].find((e) => e.subject == doc.subject);
            name.unshift((_27 = (_26 = (_25 = curVerification.topic) === null || _25 === void 0 ? void 0 : _25[0]) === null || _26 === void 0 ? void 0 : _26.label) === null || _27 === void 0 ? void 0 : _27[((_30 = (_29 = (_28 = curVerification.topic) === null || _28 === void 0 ? void 0 : _28[0]) === null || _29 === void 0 ? void 0 : _29.label) === null || _30 === void 0 ? void 0 : _30.length) - 1]);
        }
        if (key == 'mentoring:personalStatement') {
            let curVerification = userVerificationMap[key].find((e) => e.subject == doc.subject);
            name.unshift((_33 = (_32 = (_31 = curVerification.topic) === null || _31 === void 0 ? void 0 : _31[0]) === null || _32 === void 0 ? void 0 : _32.label) === null || _33 === void 0 ? void 0 : _33[((_36 = (_35 = (_34 = curVerification.topic) === null || _34 === void 0 ? void 0 : _34[0]) === null || _35 === void 0 ? void 0 : _35.label) === null || _36 === void 0 ? void 0 : _36.length) - 1]);
        }
        if (key == 'mentoring:interest') {
            let curVerification = userVerificationMap[key].find((e) => e.subject == doc.subject);
            name.unshift((_39 = (_38 = (_37 = curVerification.topic) === null || _37 === void 0 ? void 0 : _37[0]) === null || _38 === void 0 ? void 0 : _38.label) === null || _39 === void 0 ? void 0 : _39[((_42 = (_41 = (_40 = curVerification.topic) === null || _40 === void 0 ? void 0 : _40[0]) === null || _41 === void 0 ? void 0 : _41.label) === null || _42 === void 0 ? void 0 : _42.length) - 1]);
        }
        return name;
    }
    async getTeacherVerificationConfig(key) {
        var _a;
        try {
            let res = null;
            res = await this.app
                .service('conf')
                .get(key)
                .catch((e) => {
                if (e.code === 404)
                    return this.app.service('conf').create({ _id: key, val: {} });
                return null;
            });
            if ((_a = res === null || res === void 0 ? void 0 : res.curriculum) === null || _a === void 0 ? void 0 : _a.length)
                res.curriculum = (0, dict_1.sortBySysCurriculum)(res.curriculum);
            return res;
        }
        catch (error) {
            console.error(error);
        }
    }
    async authVerifyPoint(doc) {
        let { uid, _id, inviter } = doc;
        let auth = await this.Model.find({ uid, status: 2 });
        let user = await this.app.service('users').Model.findOne({ _id: uid });
        if (auth.length == 1) {
            await this.app.service('point-log').getAddLog({
                inviter: inviter,
                tab: 'earn',
                source: 'reward',
                category: 'verify',
                businessId: _id,
                snapshot: user,
            });
        }
    }
    // 用户留言
    async patchMessage({ _id, message }, params) {
        return await this.Model.updateOne({ _id }, { 'feedback.message': message, 'feedback.read': false, 'feedback.date': new Date() });
    }
    // 后台回复
    async patchReply({ _id, reply }, params) {
        return await this.Model.updateOne({ _id }, { 'feedback.reply': reply, 'feedback.replyDate': new Date() });
    }
    // 统计被多少发布的服务包关联
    async getCountPackUse({ _id }, params) {
        return await this.app.service('service-pack').Model.count({ 'contentOrientated.premium': _id, status: true });
    }
    async extFollower(doc, params) {
        if (doc.follower) {
            if (doc.schoolOfFollower) {
                doc.followerInfo = await this.app.service('school-user').Model.findOne({ uid: doc.follower, school: doc.schoolOfFollower });
            }
            else {
                doc.followerInfo = await this.app.service('users').Model.findOne({ _id: doc.follower });
            }
        }
    }
    async getGroupByFollower({ type = 'content' }, params) {
        let match = {
            follower: { $exists: true },
        };
        if (type) {
            match.type = type;
        }
        let list = await this.Model.aggregate([
            { $match: match },
            {
                $group: {
                    _id: { follower: '$follower', schoolOfFollower: '$schoolOfFollower' },
                    follower: { $first: '$follower' },
                    schoolOfFollower: { $first: '$schoolOfFollower' },
                    count: { $sum: 1 },
                },
            },
            { $sort: { count: -1 } },
        ]);
        for (let i = 0; i < list.length; i++) {
            await this.extFollower(list[i]);
        }
        return list;
    }
    // async getGroupByProduct({followed = true}: any, params: Params): Promise<any> {
    //   let match: any = {}
    //   if (followed) {
    //     match.follower = {$exists: true}
    //   } else {
    //     match.follower = {$exists: false}
    //   }
    //   return await this.Model.aggregate([
    //     {$match: match},
    //     {
    //       $group: {
    //         _id: '$serviceType',
    //         servicePack: {$first: '$servicePack'},
    //         servicePackName: {$first: '$servicePackName'},
    //         count: {$sum: 1},
    //       },
    //     },
    //     {$sort: {count: -1}},
    //   ])
    // }
    async autoRelease() {
        this.Model.find({ status: 1, followedAt: { $lt: Date.now() - 14 * 24 * 3600 * 1000 } }).then(async (rs) => {
            for (let i = 0; i < rs.length; i++) {
                const item = rs[i];
                await this.Model.updateOne({ _id: item._id }, { $unset: { follower: '', followedAt: '' }, releasedAt: new Date() });
            }
        });
    }
    async cron1({}, params) {
        this.autoRelease();
    }
    async _Find(query, params) {
        const rs = { total: 0, skip: query.$skip || 0, limit: query.$limit || 10 };
        rs.total = await this.Model.count(query);
        rs.data = Acan.clone(await this.Model.find(query).select(query.$select).sort({ _id: -1 }).skip(rs.skip).limit(rs.limit));
        for (const o of rs.data) {
            await this.ext(o);
        }
        if (isDev) {
            rs.query = query;
        }
        return rs;
    }
    // 认证精品课列表
    async getCloudList(query, params) {
        var _a;
        const uid = (_a = params.user) === null || _a === void 0 ? void 0 : _a._id;
        query.importUsers = { $ne: uid };
        query.uid = { $ne: uid };
        query.status = 2;
        query.type = 'content';
        query['unitSnapshot._id'] = { $exists: true };
        const unitSelect = this.app.service('unit').selectList.map((v) => 'unitSnapshot.' + v);
        query.$select = [...this.selectList, ...unitSelect, 'unitSnapshot._id', 'unitSnapshot.questions'];
        const rs = await this._Find(query, params);
        if (!query.$skip || query.$skip == 0) {
            query.importUsers = uid;
            const data = await this.Model.find(query).select(query.$select).limit(1000).lean();
            if (data) {
                for (const o of data) {
                    await this.ext(o);
                }
                rs.data.unshift(...data);
            }
            rs.has = data.length;
        }
        return rs;
    }
    // 面试手动return
    async getInterviewReturn({ id }) {
        let serviceAuthData = await this.Model.findOne({ _id: id });
        let { interviewInvited, takeaway } = serviceAuthData;
        if (!interviewInvited) {
            return Promise.reject(new errors_1.GeneralError('The status of the application can not be returned.'));
        }
        // takeaway解绑
        let path = Acan.parseURL(takeaway).path;
        let takeawayId = path.split('/').pop();
        let takeawayData = await this.app.service('session-takeaway-snapshot').Model.findOne({ _id: takeawayId });
        let session = await this.app.service('session').Model.findOne({ _id: takeawayData.session });
        if (session === null || session === void 0 ? void 0 : session.booking) {
            await this.app.service('service-booking').Model.updateOne({ _id: session.booking }, {
                $unset: { session: 1, serviceAuthId: 1 },
            });
        }
        if (session) {
            await this.app.service('session').remove(session._id);
        }
        return await this.Model.updateOne({ _id: id }, {
            status: 1,
            interviewInvited: false,
            interviewApply: false,
            $unset: { interviewPack: '', takeaway: '', takeawayCreatedAt: '' },
        });
    }
}
exports.ServiceAuth = ServiceAuth;
