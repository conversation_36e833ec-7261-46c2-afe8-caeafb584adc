"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const authentication = __importStar(require("@feathersjs/authentication"));
const { NotFound, GeneralError, BadRequest } = require('@feathersjs/errors');
// Don't remove this comment. It's needed to format import lines nicely.
const hook_1 = __importDefault(require("../../hook"));
const { authenticate } = authentication.hooks;
exports.default = {
    before: {
        all: [authenticate('jwt')],
        find: [],
        get: [hook_1.default.toClass],
        create: [
            async (d) => {
                let { deadline } = d.data;
                let now = new Date().getTime();
                if (new Date(deadline).getTime() < now)
                    throw new BadRequest('Deadline must be greater than now');
            },
        ],
        update: [],
        patch: [
            async (d) => {
                let { deadline } = d.data;
                let now = new Date().getTime();
                if (new Date(deadline).getTime() < now)
                    throw new BadRequest('Deadline must be greater than now');
            },
        ],
        remove: [],
    },
    after: {
        all: [],
        find: [
            async (d) => {
                if (d.result) {
                    for (let i = 0; i < d.result.data.length; i++) {
                        await d.service.extServicePack(d.result.data[i]);
                    }
                    d.result.data = d.result.data.filter((e) => e.servicePackInfo);
                }
            },
        ],
        get: [],
        create: [
            async (d) => {
                let { servicePack, deadline, students, teachers, school, role, withinSchool } = d.result;
                if (withinSchool) {
                    if (role == 'student') {
                        d.service.shareEmailStudent({ servicePack, deadline, students, school });
                    }
                    else {
                        d.service.shareEmailTeacher({ servicePack, deadline, teachers, school });
                    }
                }
            },
        ],
        update: [],
        patch: [
            async (d) => {
                let { contentOrientated } = d.data;
                let { servicePack, deadline, students, teachers, school, role, withinSchool, priceEnable } = d.result;
                if (withinSchool) {
                    if (role == 'student') {
                        d.service.shareEmailStudent({ servicePack, deadline, students, school });
                    }
                    else {
                        d.service.shareEmailTeacher({ servicePack, deadline, teachers, school });
                    }
                }
                if (priceEnable && contentOrientated) {
                    d.service.setApplyPrice({ contentOrientated, servicePack, school });
                }
            },
        ],
        remove: [],
    },
    error: {
        all: [],
        find: [],
        get: [],
        create: [],
        update: [],
        patch: [],
        remove: [],
    },
};
