"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SalesFollowUp = void 0;
const feathers_mongoose_1 = require("feathers-mongoose");
const { NotFound, GeneralError, BadRequest } = require('@feathersjs/errors');
class SalesFollowUp extends feathers_mongoose_1.Service {
    //eslint-disable-next-line @typescript-eslint/no-unused-vars
    constructor(options, app) {
        super(options);
        this.app = app;
    }
    async getGroupBySales({ type = 'following', salesType, serviceType }, params) {
        let match = {
            status: 1,
        };
        if (type) {
            match.type = type;
        }
        if (salesType) {
            match.salesType = salesType;
        }
        if (serviceType) {
            match.serviceType = serviceType;
        }
        let list = await this.Model.aggregate([
            { $match: match },
            { $group: { _id: '$sales', sales: { $first: '$sales' }, salesName: { $first: '$salesName' }, salesType: { $first: '$salesType' }, count: { $sum: 1 } } },
            { $sort: { count: -1 } },
        ]);
        for (let i = 0; i < list.length; i++) {
            await this.extUser(list[i]);
        }
        return list;
    }
    async getGroupByProduct({ status }, params) {
        let match = {};
        if (status !== undefined) {
            match.status = status;
        }
        return await this.Model.aggregate([
            { $match: match },
            {
                $group: {
                    _id: '$serviceType',
                    servicePack: { $first: '$servicePack' },
                    servicePackName: { $first: '$servicePackName' },
                    count: { $sum: 1 },
                },
            },
            { $sort: { count: -1 } },
        ]);
    }
    // stop
    async getStop({ id }, params) {
        return await this.Model.updateOne({ _id: id }, { sales: '', salesName: '', salesType: '', followedAt: '', releasedAt: new Date(), status: 0, shareCount: 0, shareGoods: [] });
    }
    // claim
    async getClaim({ id, uid, salesType = 'manager' }, params) {
        let user = await this.app.service('users').uidToInfo(uid);
        return await this.Model.updateOne({ _id: id }, { sales: uid, salesName: user.name.join(' '), salesType, followedAt: new Date(), status: 1 });
    }
    async extUser(doc, params) {
        if (doc.customer) {
            doc.customerInfo = await this.app.service('users').Model.findOne({ _id: doc.customer });
            doc.isSchool = false;
            if (!doc.customerInfo) {
                doc.customerInfo = await this.app.service('school-plan').Model.findOne({ _id: doc.customer });
                doc.isSchool = true;
            }
        }
        if (doc.sales) {
            doc.salesInfo = await this.app.service('users').Model.findOne({ _id: doc.sales });
        }
    }
    async extService(doc, params) {
        doc.servicePackInfo = await this.app.service('service-pack').Model.findOne({ _id: doc.servicePack });
        doc.servicePackUserInfo = await this.app.service('service-pack-user').Model.findOne({ _id: doc.servicePackUser });
    }
    // 发送站内信
    async getSendInbox({ uid, content }, params) {
        return await this.send(uid, content, 'InboxMessageFromFollowUpPage', params);
    }
    async send(uid, content, tpl, params) {
        var _a;
        let user = await this.app.service('users').uidToInfo(uid);
        let { email } = user;
        let sender = params.user;
        let { name, managerRoles } = sender;
        return await this.app
            .service('notice-tpl')
            .mailto(tpl, email, { username: name.join(' '), role: managerRoles[0].toFirstUpperCase(), content }, (_a = params.user) === null || _a === void 0 ? void 0 : _a._id);
    }
    async getShare({ id, goodsId, style }, params) {
        var _a, _b, _c, _d;
        let followData = await this.Model.findById(id);
        let inviter = (_a = params === null || params === void 0 ? void 0 : params.user) !== null && _a !== void 0 ? _a : {};
        let { shareCount, shareGoods, servicePack } = followData;
        if ((goodsId == servicePack && shareCount > 0) || shareGoods.includes(goodsId)) {
            return Promise.reject(new GeneralError('Already shared'));
        }
        let name;
        let school;
        let url;
        if (style == 'service') {
            let goods = await this.app.service('service-pack').Model.findById(goodsId);
            name = goods.name;
            url = `${SiteUrl}/v2/service/pack/${goodsId}?inviteCode=${inviter.inviteCode}&inviteSource=sales_follow_up`;
        }
        if (style == 'session') {
            let goods = await this.app.service('session').Model.findById(goodsId);
            name = goods.name;
            school = goods.school;
            url = `${this.app.service('session').getSessionUrl({ _id: goodsId, type: goods.type }, params)}?inviteCode=
      ${inviter.inviteCode}&inviteSource=sales_follow_up`;
        }
        if (school) {
            let student = await this.app.service('students').Model.findOne({ uid: followData.customer, school });
            if (student && ((_b = student === null || student === void 0 ? void 0 : student.parent) === null || _b === void 0 ? void 0 : _b.email)) {
                await this.app
                    .service('notice-tpl')
                    .mailto('RecommendationOfPremiumWorkshop/ServicesFromFollowUpList', student.parent.email, { username: student.parent.name.join(' '), name, url }, (_c = params.user) === null || _c === void 0 ? void 0 : _c._id);
            }
        }
        let user = await this.app.service('users').uidToInfo(followData.customer);
        if (user) {
            this.app
                .service('notice-tpl')
                .mailto('RecommendationOfPremiumWorkshop/ServicesFromFollowUpList', user.email, { username: user.name.join(' '), name, url }, (_d = params.user) === null || _d === void 0 ? void 0 : _d._id);
        }
        if (goodsId == servicePack) {
            return await this.Model.updateOne({ _id: id }, { shareCount: 1 });
        }
        else {
            return await this.Model.updateOne({ _id: id }, { $addToSet: { shareGoods: goodsId } });
        }
    }
    // 有支付处理
    async handleFollowUpPaid(packUser) {
        let packUserInfo = await this.app.service('service-pack-user').Model.findOne({ _id: packUser });
        if (packUserInfo.pid) {
            packUserInfo = await this.app.service('service-pack-user').Model.findOne({ _id: packUserInfo.pid });
        }
        let serviceType = packUserInfo.snapshot.serviceRoles;
        if (serviceType == 'mentoring' && packUserInfo.snapshot.contentOrientatedEnable) {
            serviceType = 'service_premium';
        }
        // 面试服务包 不跟踪
        if (serviceType == 'consultant' && packUserInfo.snapshot.consultant.type == 'interview') {
            return;
        }
        // 主题服务包
        let packUserDataInfo;
        if (serviceType == 'service_premium') {
            let packUserIds = (await this.app.service('service-pack-user').Model.find({ pid: packUserInfo._id })).map((item) => item._id);
            packUserDataInfo = await this.app.service('service-pack-user-data').Model.find({ packUser: { $in: packUserIds } });
        }
        else {
            packUserDataInfo = await this.app.service('service-pack-user-data').Model.find({ packUser: packUserInfo._id });
        }
        let customerName = '';
        let user = await this.app.service('users').Model.findById(packUserInfo.uid);
        if (!user) {
            user = await this.app.service('school-plan').Model.findById(packUserInfo.uid);
            customerName = user.name;
        }
        else {
            customerName = user.name.join(' ');
        }
        let followData = await this.Model.findOne({ servicePackUser: packUserInfo._id });
        let isPaid = false;
        let unused = 0;
        for (let i = 0; i < packUserDataInfo.length; i++) {
            let item = packUserDataInfo[i];
            if (item.payMethod === 'cash') {
                isPaid = true;
            }
            if (item.status === 0) {
                unused++;
            }
            if (isPaid && unused > 1) {
                break;
            }
        }
        if (!isPaid) {
            return;
        }
        // following
        let insertData = {
            customer: packUserInfo.uid,
            customerName,
            customerType: 'user',
            type: 'following',
            servicePack: packUserInfo.snapshot._id,
            servicePackName: packUserInfo.snapshot.name,
            serviceType: serviceType,
            servicePackUser: packUserInfo._id,
        };
        // completed
        if (unused > 1 || serviceType == 'service_premium') {
            insertData.type = 'completed';
            // insertData.sales = ''
            // insertData.salesName = ''
            // insertData.salesType = ''
            // insertData.status = 0
            insertData.followedAt = '';
            insertData.shareCount = 0;
            insertData.shareGoods = [];
        }
        // 有默认指定销售,进入following,设置跟踪开始时间
        if (followData && followData.type == 'completed' && insertData.type == 'following' && followData.status == 1) {
            insertData.followedAt = new Date();
        }
        await this.Model.findOneAndUpdate({
            servicePackUser: packUserInfo._id,
        }, {
            $set: insertData,
        }, { upsert: true, new: true });
    }
    // 没有支付处理 包含promotion和point
    async handleFollowUpUnpaid(booking) {
        var _a;
        let bookingData = await this.app.service('service-booking').Model.findById(booking).lean();
        let { packUser, servicer } = bookingData;
        let packUserInfo = await this.app.service('service-pack-user').Model.findOne({ _id: packUser });
        if (packUserInfo.pid) {
            packUserInfo = await this.app.service('service-pack-user').Model.findOne({ _id: packUserInfo.pid });
        }
        let serviceType = packUserInfo.snapshot.serviceRoles;
        if (serviceType == 'mentoring' && packUserInfo.snapshot.contentOrientatedEnable) {
            serviceType = 'service_premium';
        }
        // 面试服务包 不跟踪
        if (serviceType == 'consultant' && packUserInfo.snapshot.consultant.type == 'interview') {
            return;
        }
        // 主题服务包
        let packUserDataInfo;
        if (serviceType == 'service_premium') {
            let packUserIds = (await this.app.service('service-pack-user').Model.find({ pid: packUserInfo._id })).map((item) => item._id);
            packUserDataInfo = await this.app.service('service-pack-user-data').Model.find({ packUser: { $in: packUserIds } });
        }
        else {
            packUserDataInfo = await this.app.service('service-pack-user-data').Model.find({ packUser: packUserInfo._id });
        }
        let customerName = '';
        let user = await this.app.service('users').Model.findById(packUserInfo.uid);
        if (!user) {
            user = await this.app.service('school-plan').Model.findById(packUserInfo.uid);
            customerName = user.name;
        }
        else {
            customerName = user.name.join(' ');
        }
        let servicerConfData = await this.app.service('service-conf').Model.findById(servicer);
        let followData = await this.Model.findOne({ servicePackUser: packUserInfo._id });
        for (let i = 0; i < packUserDataInfo.length; i++) {
            let item = packUserDataInfo[i];
            if (item.payMethod === 'cash') {
                return;
            }
        }
        if (followData && followData.status == 1) {
            return;
        }
        // following
        let insertData = {
            customer: packUserInfo.uid,
            customerName,
            customerType: 'user',
            type: 'following',
            status: 0,
            servicePack: packUserInfo.snapshot._id,
            servicePackName: packUserInfo.snapshot.name,
            serviceType: serviceType,
            servicePackUser: packUserInfo._id,
        };
        if ((_a = servicerConfData === null || servicerConfData === void 0 ? void 0 : servicerConfData.serviceRoles) === null || _a === void 0 ? void 0 : _a.includes('consultant')) {
            let servicerData = await this.app.service('users').Model.findById(servicer);
            insertData.sales = servicer;
            insertData.salesName = servicerData.name.join(' ');
            insertData.salesType = 'consultant';
            insertData.status = 1;
            insertData.followedAt = new Date();
        }
        else {
            if (followData) {
                return;
            }
        }
        await this.Model.findOneAndUpdate({
            servicePackUser: packUserInfo._id,
        }, {
            $set: insertData,
        }, { upsert: true, new: true });
    }
    async autoRelease() {
        this.Model.find({ status: 1, type: 'following', followedAt: { $lt: Date.now() - 14 * 24 * 3600 * 1000 } }).then(async (rs) => {
            for (let i = 0; i < rs.length; i++) {
                const item = rs[i];
                await this.getStop({ id: item._id });
            }
        });
    }
    async cron1({}, params) {
        this.autoRelease();
    }
}
exports.SalesFollowUp = SalesFollowUp;
