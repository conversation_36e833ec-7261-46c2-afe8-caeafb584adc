"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const reviews_class_1 = require("./reviews.class");
const reviews_model_1 = __importDefault(require("../../models/reviews.model"));
const reviews_hooks_1 = __importDefault(require("./reviews.hooks"));
function default_1(app) {
    const options = {
        Model: (0, reviews_model_1.default)(app),
        paginate: app.get('paginate')
    };
    // Initialize our service with any options it requires
    app.use('/reviews', new reviews_class_1.Reviews(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('reviews');
    service.hooks(reviews_hooks_1.default);
}
exports.default = default_1;
