import {HookContext} from '@feathersjs/feathers'
import * as authentication from '@feathersjs/authentication'
import hook from '../../hook'
import logger from '../../logger'
// Don't remove this comment. It's needed to format import lines nicely.

const {authenticate} = authentication.hooks

export default {
  before: {
    all: [],
    find: [
      authenticate('jwt'),
      async (d: HookContext) => {
        const query: any = d.params.query
        // tab: booker: 预订列表, servicer: 服务者看的预约列表
        await hook.sysQuery(query.tab)(d)
      },
    ],
    get: [
      hook.toClass,
      (d: HookContext) => {
        if (!Acan.isEmpty(d.params.query)) {
          d.params.extQuery = d.params.query
          d.params.query = {}
        }
      },
    ],
    create: [
      authenticate('jwt'),
      async (d: HookContext) => {
        d.data.booker = d.params.user?._id
        delete d.data.session
        await d.service.checkHours(d.data, d.params)
        // 判断服务包次数是否足够
        await d.app.service('service-pack-user').checkTimes({_id: d.data.packUser, times: parseInt(d.data.times)})
        // 获取服务包类型
        const {snapshot} = await d.app.service('service-pack-user').Model.findById(d.data.packUser).select(['snapshot.type', 'snapshot.mentoringType'])
        Object.assign(d.data, snapshot)
      },
    ],
    update: [hook.disable],
    patch: [
      /** @transactional | `params` 可能包含一个 Mongoose session，所有数据库调用以及新添加的嵌套函数必须使用该 session。*/ authenticate('jwt'),
      hook.toClass,
    ],
    remove: [authenticate('jwt'), hook.disable],
  },

  after: {
    all: [],
    find: [
      async (d: HookContext) => {
        for (const o of d.result.data) {
          await d.service.extUser(o, true)
        }
        if (isDev) d.result.query = d.params.query
        return d
      },
    ],
    get: [
      async (d: HookContext) => {
        if (hook.classExist(d)) return d
        await d.service.extUser(d.result)
        await d.service.extSession(d.result, d.params)
      },
    ],
    create: [
      async (d: HookContext) => {
        const {_id, type, packUser, servicePackApply, times, servicer, start, oldSession} = d.result
        // 创建预约，扣除服务包次数 service-pack-user.used += service-booking.times
        await d.app.service('service-pack-user-data').used({booking: _id.toString(), packUser, times, type: 'booking', servicer, oldSession, start}, d.params)
        if (type === 'content') await d.service.importLecture(d.result) // Lecture 自动排课 https://github.com/zran-nz/bug/issues/79
        else if (servicePackApply) await d.service.autoCreateSession(d.result) // 面试服务包自动排课
      },
    ],
    update: [],
    patch: [
      /** @transactional | `params` 可能包含一个 Mongoose session，所有数据库调用以及新添加的嵌套函数必须使用该 session。*/
      (d: HookContext) => {
        if (hook.classExist(d)) return d
      },
    ],
    remove: [],
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: [],
  },
}
