"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Tool = void 0;
const errors_1 = require("@feathersjs/errors");
class Tool {
    constructor(options = {}, app) {
        this.options = options;
        this.app = app;
    }
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    async find(params) {
        return [];
    }
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    async get(id, params) {
        return { id, text: `A new message with ID: ${id}!`, default: true };
    }
    getError({ type }) {
        if (type === '400')
            return Promise.reject(new errors_1.BadRequest('code', { msg: 'BadRequest message' }));
        if (type === '500')
            return Promise.reject(new errors_1.GeneralError('GeneralError message', { data: '2222' }));
        if (type === '404')
            return Promise.reject(new errors_1.NotFound('NotFound message'));
        else
            return Promise.reject(new Error('Error message'));
    }
    async getModel({}, params) {
        const list = [];
        const services = this.app.services;
        for (const key in services) {
            if (services[key].Model)
                list.push(key);
        }
        return { list };
    }
    async getCron({ min }, params) {
        var _a;
        if (params.Aip.substr(0, 7) !== '172.31.')
            return { ip: params.Aip, message: 'Invalid ip' };
        const post = {
            ip: params.Aip || global.LocalIp,
            ttl: Date.now() - params._startTime,
            type: ['node.cron', min].join('.'),
            ua: (_a = params.headers) === null || _a === void 0 ? void 0 : _a['user-agent'],
        };
        this.app
            .service('session')
            .cron1({}, params)
            .then((rs) => {
            if ((rs === null || rs === void 0 ? void 0 : rs.length) > 0)
                this.app.service('log').create({ ...post, body: rs, msg: `session ok, ${rs.length}` });
            return {};
        });
        this.app.service('order').cron1({}, params);
        this.app.service('school-plan').cron1({}, params);
        this.app.service('sales-follow-up').cron1({}, params);
        this.app.service('suspend-class').cron1({}, params);
        this.app.service('service-booking').cron1({}, params);
        this.app.service('service-pack-user-data').cron1({}, params);
        this.app.service('service-pack-school-price').cron1({}, params);
        this.app.service('service-auth').cron1({}, params);
        this.app.service('service-conf').cron1({}, params);
        this.app.service('service-pack-apply').cron1({}, params);
        return { ip: params.Aip, min, ttl: Date.now() - params._startTime, date: new Date().toString() };
    }
    async getClearCache({ keys }) {
        const arr = keys || [
            'SchoolInfo',
            'OrderCount',
            'email:_id',
            'email:user',
            'uid:_id',
            '_id:info',
            'StatSchool',
            'StatUser',
            'reviewStat:rates',
            'reviewStat:grades',
        ];
        // 'schoolUser:*', 'students:Info*'
        const result = await this.app.get('redis').DEL(...arr);
        return { result, arr };
    }
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    async create(data, params) {
        if (Array.isArray(data)) {
            return Promise.all(data.map((current) => this.create(current, params)));
        }
        return data;
    }
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    async update(id, data, params) {
        return data;
    }
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    async patch(id, data, params) {
        return data;
    }
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    async remove(id, params) {
        return { id };
    }
}
exports.Tool = Tool;
