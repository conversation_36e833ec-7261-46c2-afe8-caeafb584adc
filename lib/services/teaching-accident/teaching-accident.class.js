"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TeachingAccident = void 0;
const feathers_mongoose_1 = require("feathers-mongoose");
const { NotFound, GeneralError, BadRequest } = require('@feathersjs/errors');
class TeachingAccident extends feathers_mongoose_1.Service {
    //eslint-disable-next-line @typescript-eslint/no-unused-vars
    constructor(options, app) {
        super(options);
        this.app = app;
    }
    // 未读统计
    async getUnreadCount({}, params) {
        let list = await this.Model.find({ read: false });
        let dict = {};
        for (let i = 0; i < list.length; i++) {
            const item = list[i];
            for (let j = 0; j < item.tags.length; j++) {
                const tag = item.tags[j];
                if (dict[tag]) {
                    dict[tag]++;
                }
                else {
                    dict[tag] = 1;
                }
            }
        }
        return {
            unread: list.length,
            detail: dict,
        };
    }
    async handleApprove({ _id, data }, params) {
        let res = await this.Model.findOne({ _id });
        let { status, days = 0 } = data;
        let { endAt } = await this.app.service('suspend-class').suspend({ type: 'teaching-accident', accidentId: _id, status, days, uid: res.teacher });
        res.endAt = endAt;
        this.send(res, 'TeachingAccidentApproved', params, days == 0 ? true : false);
        if (!res.serviceReturn) {
            let oldSession = await this.app.service('session').Model.findOne({ _id: res.session });
            let bookingData = await this.app.service('service-booking').Model.findOne({ _id: res.serviceBooking });
            let serviceData = await this.app.service('service-pack-user-data').Model.find({ _id: { $in: bookingData.packUserData } });
            for (let i = 0; i < serviceData.length; i++) {
                const item = serviceData[i];
                this.app.service('service-pack-user-data').add({
                    packUser: res.servicePackUser,
                    type: 'teachingAccident',
                    times: 1,
                    payMethod: 'gift',
                    order: item.order,
                    serviceTicket: item.serviceTicket,
                    servicer: res.teacher,
                    oldSession,
                }, params);
            }
            await this.Model.updateOne({ _id }, { serviceReturn: true });
        }
    }
    async extData(one, params) {
        one.studentInfo = await this.app.service('users').uidToInfo(one.student);
        one.teacherInfo = await this.app.service('users').uidToInfo(one.teacher);
        const suspend = await this.app.service('suspend-class').Model.findOne({ accident: one._id });
        one.suspend = suspend;
    }
    async send(doc, tpl, params, withoutSuspension = false) {
        var _a, _b, _c;
        const { session, sessionName, student, teacher, checkReason, serviceType, serviceName, tags, evidencesStudent, endAt } = doc;
        const studentInfo = await this.app.service('users').uidToInfo(student);
        const teacherInfo = await this.app.service('users').uidToInfo(teacher);
        let url = '';
        let url2 = '';
        if (tpl === 'TeachingAccidentReported') {
            url = `${SiteUrl}/v2/detail/session/${session}?evaluation=true`;
        }
        else if (tpl === 'TeachingAccidentApproved') {
            url = `${SiteUrl}/help/#/main/terms`;
            url2 = `${SiteUrl}/v2/detail/session/${session}`;
        }
        else if (tpl === 'TeachingAccidentRejected') {
            url = `${SiteUrl}/help/#/main/terms`;
            url2 = `${SiteUrl}/v2/detail/session/${session}`;
        }
        let needSuffix = false;
        if (tpl === 'TeachingAccidentReported') {
            needSuffix = true;
        }
        // 发给学生
        await this.app.service('notice-tpl').mailto(tpl + `${needSuffix ? 'Student' : ''}`, studentInfo.email, {
            username: studentInfo.name.join(' '),
            session_name: sessionName,
            check_reason: checkReason,
            url,
            service_type: serviceType,
            service_name: serviceName,
            tags: tags.join(','),
            reason: evidencesStudent[0].content,
            start: new Date().toLocaleDateString(),
            end: new Date(endAt).toLocaleDateString(),
            student_name: studentInfo.name.join(' '),
            teacher_name: teacherInfo.name.join(' '),
            url2,
        }, (_a = params.user) === null || _a === void 0 ? void 0 : _a._id);
        // 发给老师
        await this.app.service('notice-tpl').mailto(tpl + `${needSuffix ? 'Teacher' : ''}`, teacherInfo.email, {
            username: teacherInfo.name.join(' '),
            session_name: sessionName,
            check_reason: checkReason,
            url,
            service_type: serviceType,
            service_name: serviceName,
            tags: tags.join(','),
            reason: evidencesStudent[0].content,
            start: new Date().toLocaleDateString(),
            end: new Date(endAt).toLocaleDateString(),
            student_name: studentInfo.name.join(' '),
            teacher_name: teacherInfo.name.join(' '),
            url2,
        }, (_b = params.user) === null || _b === void 0 ? void 0 : _b._id);
        // 有停课,发送额外通知给老师
        if (!withoutSuspension && tpl === 'TeachingAccidentApproved') {
            await this.app.service('notice-tpl').mailto('TeachingAccidentApprovedWithSuspensionTeacher', teacherInfo.email, {
                username: teacherInfo.name.join(' '),
                session_name: sessionName,
                check_reason: checkReason,
                url,
                service_type: serviceType,
                service_name: serviceName,
                tags: tags.join(','),
                reason: evidencesStudent[0].content,
                start: new Date().toLocaleDateString(),
                end: new Date(endAt).toLocaleDateString(),
                student_name: studentInfo.name.join(' '),
                teacher_name: teacherInfo.name.join(' '),
                url2,
            }, (_c = params.user) === null || _c === void 0 ? void 0 : _c._id);
        }
    }
}
exports.TeachingAccident = TeachingAccident;
