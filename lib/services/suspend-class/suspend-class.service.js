"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const suspend_class_class_1 = require("./suspend-class.class");
const suspend_class_model_1 = __importDefault(require("../../models/suspend-class.model"));
const suspend_class_hooks_1 = __importDefault(require("./suspend-class.hooks"));
function default_1(app) {
    const options = {
        Model: (0, suspend_class_model_1.default)(app),
        paginate: app.get('paginate'),
    };
    // Initialize our service with any options it requires
    app.use('/suspend-class', new suspend_class_class_1.SuspendClass(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('suspend-class');
    service.hooks(suspend_class_hooks_1.default);
}
exports.default = default_1;
