"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Students = void 0;
const feathers_mongoose_1 = require("feathers-mongoose");
const logger_1 = __importDefault(require("../../logger"));
const { GeneralError } = require('@feathersjs/errors');
const errors_1 = require("@feathersjs/errors");
class PlanLimit extends errors_1.FeathersError {
    constructor(message, name, data) {
        super(message, name || 'PlanLimit', 451, 'PlanLimit', data);
    }
}
class Students extends feathers_mongoose_1.Service {
    //eslint-disable-next-line @typescript-eslint/no-unused-vars
    constructor(options, app) {
        super(options);
        this.app = app;
    }
    async randId() {
        const id = Acan.random(10000000, 99999999);
        const num = await this.Model.count({ id });
        if (num > 0)
            return await this.randId();
        return id;
    }
    getInfo({ school, email }, params) {
        return this.app.get('redisHCache')('students:Info' + school, email, async () => {
            const rs = Acan.clone(await this.Model.findOne({ school, email }).select(['nickname', 'name', 'avatar', 'email', 'gender', 'dob', 'id', 'password']));
            if (!rs)
                return null;
            rs.nickname = rs.name.join(' ');
            if (!rs.avatar)
                rs.avatar = '';
            return rs;
        });
    }
    // need remove
    async autoCreateUser(data, params) {
        if (data.email)
            return; // has email
        const password = Acan.random(100000, 999999) + '';
        data.password = password;
        data.email = `${data.id}@classcipe.com`;
        const rs = await this.app.service('users').create({
            studentId: data.id,
            name: data.name,
            password,
            email: data.email,
            roles: ['student'],
        });
        data.uid = rs._id;
        return rs;
    }
    async extClass(one, params) {
        var _a;
        let classInfo = [];
        if ((_a = one.class) === null || _a === void 0 ? void 0 : _a.length) {
            for (const classId of one.class) {
                const classData = await this.app.service('classes').Model.findOne({ _id: classId });
                classInfo.push(classData);
            }
        }
        one.classInfo = classInfo;
    }
    // 统计学校下的学生数量
    async count({ school }, params) {
        const count = await this.Model.count({ school, del: false });
        await this.app.service('school-plan').Model.updateOne({ _id: school }, { 'count.student': count });
    }
    // 统计每个班级下的学生数量
    async countClass({ class: classes }, params) {
        if (Acan.isEmpty(classes))
            return;
        if (!Array.isArray(classes))
            classes = [classes];
        for (const classId of classes) {
            if (classId != 'cloudRoom') {
                const count = await this.Model.count({ class: classId, del: false });
                await this.app.service('classes').Model.updateOne({ _id: classId }, { 'count.student': count });
            }
        }
    }
    // 统计每个班级下的学生数量 学科班
    async countSubjectClass({ subjectClass }, params) {
        if (Acan.isEmpty(subjectClass))
            return;
        if (!Array.isArray(subjectClass))
            subjectClass = [subjectClass];
        for (const classId of subjectClass) {
            if (classId != 'cloudRoom') {
                const count = await this.Model.count({ subjectClass: subjectClass, del: false });
                await this.app.service('classes').Model.updateOne({ _id: classId }, { 'count.student': count });
            }
        }
    }
    async autoJoinByLogin({ id, uid }) {
        await this.Model.updateOne({ id, status: 0 }, { uid, status: 2 });
    }
    // 通过邀请链接加入学校，需要审核
    async getApply({ school, name, avatar }, params) {
        var _a, _b;
        const { _id, email } = (_a = params.user) !== null && _a !== void 0 ? _a : {};
        // 老师身份不能申请
        if (!((_b = params.user) === null || _b === void 0 ? void 0 : _b.role.includes('student')))
            return Promise.reject(new GeneralError({ param: 'role', message: 'User Role error' }));
        const old = await this.Model.findOne({ school, uid: _id });
        if (old) {
            // await this.Model.updateOne({_id: old._id}, {status: 2})
            return old;
        }
        // 学生通过链接申请加入学校
        return await this.create({ school, uid: _id, name, nickname: name.join(' '), avatar, email, status: 1 }, params);
    }
    async getJoin({ _id }, params) {
        var _a, _b, _c, _d;
        const uid = (_a = params.user) === null || _a === void 0 ? void 0 : _a._id;
        const studentData = await this.Model.findById(_id);
        let { email, mobile, id } = studentData;
        if (email && email !== ((_b = params.user) === null || _b === void 0 ? void 0 : _b.email))
            return await Promise.reject(new PlanLimit('Your email address does not match the activation account', 'EmailError'));
        else if (mobile && mobile !== ((_c = params.user) === null || _c === void 0 ? void 0 : _c.mobile))
            return await Promise.reject(new PlanLimit('Your mobile does not match the activation account', 'MobileError'));
        else if (id && id.toString() !== ((_d = params.user) === null || _d === void 0 ? void 0 : _d.studentId))
            return await Promise.reject(new PlanLimit('Your Student Id does not match the activation account', 'IdError'));
        await this.Model.updateOne({ _id }, { uid }); // 绑定学生帐号 user._id
        this.sendNotice(studentData, 'StudentAddedToStandardClass', params);
        return await this.Model.updateOne({ _id, status: 0 }, { uid, status: 2 });
    }
    async getResend({ _id }, params) {
        let { email, parent, mobile } = await this.Model.findById(_id);
        if ((!email || /^\<EMAIL>$/.test(email)) && !mobile) {
            // 学生邮箱不存在，发送给家长
            return await this.app.service('notice-tpl').resend({ code: 'SchoolStudentCreateToParent', email: parent.email }, params);
        }
        else {
            // 直接发送给学生
            let lastNotice = await this.app
                .service('notice')
                .Model.findOne({ $and: [{ $or: [{ code: 'SchoolStudentCreate' }, { code: 'SchoolStudentCreate(sms)' }] }, { $or: [{ to: email }, { to: mobile }] }] })
                .sort({ _id: -1 });
            return await this.app.service('notice-tpl').resend({ code: lastNotice.code, email, mobile }, params);
        }
    }
    // by create
    async sendAll(arr, idList, params) {
        const id = arr[0].school;
        const schoolInfo = await this.app.service('school-plan').getInfo({ school: id }, params);
        logger_1.default.warn('Students sendAll:', id, arr, schoolInfo);
        const rs = [];
        for (const o of arr) {
            let hasId = idList.includes(o.id);
            rs.push(await this.send(o, 'SchoolStudentCreate', params, schoolInfo, hasId));
        }
        return rs;
    }
    async send(doc, tpl, params, schoolInfo, hasId = false) {
        var _a;
        let { _id, school, name, email, parent, id, password, mobile } = doc;
        let url = '';
        let tip = '';
        // 查询学生是否已有登录账户
        let user;
        if (email) {
            user = await this.app.service('users').Model.findOne({ email });
        }
        if (mobile) {
            user = await this.app.service('users').Model.findOne({ mobile });
        }
        if ((!email || /^\<EMAIL>$/.test(email)) && !mobile) {
            // 学生邮箱不存在，发送给家长
            tpl = 'SchoolStudentCreateToParent';
            email = parent.email;
            url = `${SiteUrl}/v2/com/schoolParentJoin/${Acan.base64encode(JSON.stringify({ _id, email: parent.email }))}`;
            if (hasId) {
                // 创建携带了学生ID
                // tpl = 'SchoolStudentCreateToParent(37a)'
                tip = 'If your child changed the password, please use the updated one.';
            }
        }
        if (!schoolInfo)
            schoolInfo = await this.app.service('school-plan').getInfo({ school });
        if (!schoolInfo)
            schoolInfo = await this.app.service('users').uidToInfo(school);
        if (tpl === 'SchoolStudentCreate') {
            if (user) {
                url = `${SiteUrl}/v2/login?accountId=${email}&back=/v2/account/schoolStudentJoin/${_id}`;
            }
            else {
                // 无登录账户,跳转至学生注册页面
                url = `${SiteUrl}/v2/student/signup?back=/v2/account/schoolStudentJoin/${_id}`;
            }
        }
        // 学生未注册且用手机添加,发送短信通知
        if (mobile && !user) {
            tpl = 'SchoolStudentCreate(sms)';
        }
        return await this.app
            .service('notice-tpl')
            .mailto(tpl, { email, _id: user === null || user === void 0 ? void 0 : user._id }, { studentName: name.join(' '), nickname: name.join(' '), schoolName: schoolInfo.name, url, id, password, email, tip }, (_a = params.user) === null || _a === void 0 ? void 0 : _a._id, mobile);
    }
    async getCheckEmail(query, params) {
        return await this.Model.count(query);
    }
    // 批量检查 学生邮箱验证
    async getCheckEmails(query, params) {
        // 检查是否是老师
        let arr = await this.app.service('users').Model.aggregate([
            {
                $match: { email: query.email, roles: { $ne: 'student' } },
            },
            {
                $group: { _id: '$email', count: { $sum: 1 } },
            },
        ]);
        const teacher = {};
        for (const o of arr) {
            teacher[o._id] = o.count;
        }
        const { student } = await this.getCheckAll(query, 'email');
        return { teacher, student };
    }
    async getCheckMobile(query) {
        return await this.Model.count(query);
    }
    async getCheckMobiles(query) {
        return await this.getCheckAll(query, 'mobile');
    }
    // 批量检查 学生验证
    async getCheckAll(query, key) {
        // 检查学校下是否已经存在
        const arr = await this.Model.aggregate([
            { $match: query },
            {
                $group: { _id: '$' + key, count: { $sum: 1 } },
            },
        ]);
        const student = {};
        for (const o of arr) {
            student[o._id] = o.count;
        }
        return { student };
    }
    async getCheckId(query) {
        return await this.Model.count(query);
    }
    // 批量检查 学生ID验证
    async getCheckIds(query) {
        return await this.getCheckAll(query, 'id');
    }
    // 批量检查 学校学生姓名+家长邮箱不重复验证
    async getCheckParents(query, params) {
        const arr = await this.Model.aggregate([{ $match: query }, { $group: { _id: { name: '$name', email: '$parent.email' }, count: { $sum: 1 } } }]);
        const list = {};
        for (const o of arr) {
            list[`${o._id.email}:${o._id.name.join(' ')}`] = o.count;
        }
        return list;
    }
    async mailModify({ name, email, parent }, params, id) {
        var _a, _b, _c, _d;
        const noticeTpl = this.app.service('notice-tpl');
        if (email || (parent === null || parent === void 0 ? void 0 : parent.email)) {
            const doc = await this.Model.findById(id);
            const schoolInfo = await this.app.service('school-plan').getInfo({ school: doc.school });
            const mailData = { studentName: name.join(' '), schoolName: schoolInfo.name, url: '' };
            // parent email modify
            if ((parent === null || parent === void 0 ? void 0 : parent.email) && ((_a = doc === null || doc === void 0 ? void 0 : doc.parent) === null || _a === void 0 ? void 0 : _a.email) !== parent.email.trim()) {
                parent.email = parent.email.trim();
                if (doc.status === 2) {
                    mailData.url = `${SiteUrl}/v2/com/schoolParentEmailChange/${doc._id}`;
                    noticeTpl.mailto('SchoolStudentParentModify', parent.email, mailData, (_b = params.user) === null || _b === void 0 ? void 0 : _b._id).then();
                }
                else {
                    Object.assign(mailData, { id: doc.id, password: doc.password });
                    mailData.url = `${SiteUrl}/v2/com/schoolParentJoin/${Acan.base64encode(JSON.stringify({ _id: doc._id, email: parent.email }))}`;
                    noticeTpl.mailto('SchoolStudentCreateToParent', parent.email, mailData, (_c = params.user) === null || _c === void 0 ? void 0 : _c._id).then();
                }
            }
            // student email modify
            if (doc.email !== email) {
                mailData.url = `${SiteUrl}/v2/account/schoolStudentJoin/${doc._id}`;
                noticeTpl.mailto('SchoolStudentModify', email, mailData, (_d = params.user) === null || _d === void 0 ? void 0 : _d._id).then();
            }
        }
    }
    async getClassList({ school }, params) {
        var _a;
        const doc = await this.Model.findOne({ email: (_a = params.user) === null || _a === void 0 ? void 0 : _a.email, school, del: false, status: 2 }).select(['class', 'subjectClass']);
        if (!doc || (Acan.isEmpty(doc.class) && Acan.isEmpty(doc.subjectClass)))
            return [];
        let classList = doc.class || [];
        if (!Acan.isEmpty(doc.subjectClass)) {
            classList = [...classList, ...doc.subjectClass];
        }
        return await this.app.service('classes').Model.find({ _id: { $in: classList } });
    }
    async getSchoolList(query, params) {
        var _a, _b;
        if (!((_a = params.user) === null || _a === void 0 ? void 0 : _a._id))
            return [];
        const select = ['name', 'avatar', 'nickname', 'school', 'role', 'uid', 'email'];
        const rs = Acan.clone(await this.Model.find({ uid: (_b = params.user) === null || _b === void 0 ? void 0 : _b._id, del: false, status: 2 }).select(select));
        const arr = [];
        for (const one of rs) {
            const schoolInfo = await this.app.service('school-plan').getInfo({ school: one.school }, params);
            if (!schoolInfo)
                continue; // old school filter
            Object.assign(one, { schoolInfo, school: schoolInfo._id });
            arr.push(one);
        }
        return arr;
    }
    // 激活家长的邮件，并返回学生的账号密码
    async getParentJoin({ code }, params) {
        const { _id, email } = JSON.parse(Acan.base64decode(code));
        const rs = await this.Model.findById(_id);
        if (!rs)
            return Promise.reject(new Error('student not found'));
        if (email && rs.parent.email !== email)
            return Promise.reject(new Error('parent email error'));
        if (rs.parent.status !== 2)
            await this.patch(_id, { 'parent.status': 2 }, { inside: true });
        return rs;
    }
    async getCloudRoomCount({ school }, params) {
        let count = await this.Model.count({ school, class: 'cloudRoom' });
        return {
            count,
        };
    }
    async sendNoticeAll(arr, tpl, params) {
        for (let i = 0; i < arr.length; i++) {
            const student = arr[i];
            this.sendNotice(student, tpl, params);
        }
    }
    async sendNotice(doc, tpl, params) {
        var _a;
        let { name, email } = doc;
        let classInfo = await this.app.service('classes').Model.findOne({ _id: doc.class[0] });
        let url;
        if (tpl === 'StudentAddedToStandardClass') {
            url = `${SiteUrl}/v2/account/student/all?classId=${doc.class[0]}`;
        }
        return await this.app.service('notice-tpl').mailto(tpl, email, { studentName: name.join(' '), className: classInfo.name, url }, (_a = params === null || params === void 0 ? void 0 : params.user) === null || _a === void 0 ? void 0 : _a._id);
    }
    // 退出学科班 并清理报名记录
    async clearSubjectClass({ doc }) {
        await this.Model.updateOne({ _id: doc._id }, { subjectClass: [] });
        await this.app.service('class-apply').Model.deleteMany({ student: doc._id });
    }
}
exports.Students = Students;
