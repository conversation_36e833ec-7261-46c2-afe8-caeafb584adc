import {HookContext} from '@feathersjs/feathers'
import * as authentication from '@feathersjs/authentication'
import logger from '../../logger'
// Don't remove this comment. It's needed to format import lines nicely.
const {authenticate} = authentication.hooks
const {GeneralError} = require('@feathersjs/errors')
import {mod} from './slides.mod'
import hook from '../../hook'

export default {
  before: {
    all: [],
    find: [authenticate('jwt')],
    get: [
      hook.toClass,
      async (d: HookContext) => {
        if (hook.classExist(d)) return d
        if (d.id && mod[d.id]) return mod[d.id](d)
        if (!Acan.isObjectId(d.id)) {
          // 通过slides.id查询
          d.result = await d.service.Model.findOne({id: d.id})
        }
        return d
      },
    ],
    create: [
      /** @transactional | `params` 可能包含一个 Mongoose session，所有数据库调用以及新添加的嵌套函数必须使用该 session。*/
      authenticate('jwt'),
      (d: HookContext) => {
        const {id, rev, pages} = d.data
        if (!id) return (d.result = {message: 'no id'}), d
        // if (!Array.isArray(pages) || Acan.isEmpty(pages)) return Promise.reject(new GeneralError(new Error('pages is empty')))
        // if (id && rev) d.data.hash = Acan.SHA1(id+rev)
        logger.info('slides.create', d.data.pages?.length)
      },
    ],
    update: [authenticate('jwt')],
    patch: [authenticate('jwt')],
    remove: [authenticate('jwt')],
  },

  after: {
    all: [],
    find: [
      (d: HookContext) => {
        if (d.result) for (const one of d.result.data) d.service.picFormat(one)
      },
    ],
    get: [
      (d: HookContext) => {
        if (hook.classExist(d)) return d
        d.service.picFormat(d.result)
      },
    ],
    create: [
      /** @transactional | `params` 可能包含一个 Mongoose session，所有数据库调用以及新添加的嵌套函数必须使用该 session。*/
      async (d: HookContext) => {
        // d.service.urlToS3(d.result)
      },
    ],
    update: [],
    patch: [],
    remove: [],
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [
      async (d: any) => {
        const {code} = d.error
        if (code === 409) {
          // 对存在的数据，进行局部更新
          // const { pages } = d.data
          // const old = await d.service.Model.findOne({ hash })
          // const oldList: any = {}
          // for (const page of old.pages) {
          //   if (!page.pic) continue
          //   oldList[page.url] = page.pic
          // }
          // // url未变化的ppt 使用老的 s3地址
          // for (const page of pages) {
          //   if (oldList[page.url]) page.pic = oldList[page.url]
          // }
          // await d.service.Model.updateOne({ _id: old._id }, { $set: { pages } })
          // d.service.urlToS3({ _id: old._id, pages })
          // d.result = { ...old, pages }
          // d.error = null
        }
      },
    ],
    update: [],
    patch: [],
    remove: [],
  },
}
