"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const authentication = __importStar(require("@feathersjs/authentication"));
const hook_1 = __importDefault(require("../../hook"));
// Don't remove this comment. It's needed to format import lines nicely.
const { authenticate } = authentication.hooks;
exports.default = {
    before: {
        all: [],
        find: [
            async (d) => {
                var _a, _b, _c;
                const query = (_a = d.params.query) !== null && _a !== void 0 ? _a : {};
                if (query.uid !== '1')
                    await authenticate('jwt')(d);
                if (!query.uid)
                    query.uid = (_b = d.params.user) === null || _b === void 0 ? void 0 : _b._id;
                // if (query.uid !== '1' && !hook.roleHas(['sys'])(d)) query.uid = d.params.user?._id
                query.$select = (_c = query.$select) !== null && _c !== void 0 ? _c : d.service.selectList;
                if (query.isLib) {
                    query.$select = hook_1.default.selectLib(query.$select, d.service.publishKeys);
                    d.params.isLib = true;
                    query.del = false;
                    // query['snapshot._id'] = {$exists: true}
                }
                delete query.isLib;
            },
        ],
        get: [
            async (d) => {
                var _a;
                if (!Acan.isObjectId(d.id)) {
                    await authenticate('jwt')(d);
                    return await hook_1.default.toClass(d);
                }
                const query = (_a = d.params.query) !== null && _a !== void 0 ? _a : {};
                if (query.isLib)
                    d.params.isLib = true;
                delete query.isLib;
            },
        ],
        create: [
            authenticate('jwt'),
            (d) => {
                var _a;
                d.data.uid = d.data.uid || ((_a = d.params.user) === null || _a === void 0 ? void 0 : _a._id);
            },
        ],
        update: [authenticate('jwt'), hook_1.default.disable],
        patch: [
            authenticate('jwt'),
            async (d) => {
                var _a;
                const { snapshot } = (_a = d.data) !== null && _a !== void 0 ? _a : {};
                if (snapshot) {
                    await d.service.setSnapshot(d.id, d.data);
                }
                if (d.data.topic) {
                    let originData = await d.service.Model.findOne({ _id: d.id }).select('topic');
                    const redis = d.app.get('redis');
                    redis.set(`topic:${d.id}`, JSON.stringify(originData.topic));
                }
            },
        ],
        remove: [authenticate('jwt')],
    },
    after: {
        all: [],
        find: [
            (d) => {
                if (d.params.isLib)
                    d.result.data = hook_1.default.resultLib(d.result.data, d.service.publishKeys);
            },
        ],
        get: [
            (d) => {
                if (d.params.isLib) {
                    hook_1.default.resultLibOne(d.result, d.service.publishKeys);
                    return d;
                }
            },
        ],
        create: [
            (d) => {
                if (d.data.curriculum) {
                    const { uid, _id } = Acan.clone(d.result);
                    // d.app.service('curric').countSubjects({school: uid, subject: _id, curriculum: d.data.curriculum})
                    d.app.service('curric').count({ school: uid, model: 'subjects' });
                }
            },
        ],
        update: [],
        patch: [
            async (d) => {
                const { uid, _id } = Acan.clone(d.result);
                if (d.data.curriculum) {
                    d.app.service('curric').count({ school: uid, model: 'subjects' });
                    // d.app.service('curric').countSubjects({school: uid, subject: _id, curriculum: d.data.curriculum})
                }
                const redis = d.app.get('redis');
                let oldData = JSON.parse(await redis.get(`topic:${d.id}`));
                redis.del(`topic:${d.id}`);
                let { topic: newData } = d.data;
                let { name } = d.result;
                if (name === 'Teacher training' && newData && uid == 1) {
                    let res = d.service.matchTopic(oldData, newData);
                    if (res.labels.length > 0) {
                        d.service.sendAll(d.result, 'VerificationStatusChanged', res.labels, d.params);
                    }
                }
                if (name === 'Essay' && newData && uid == 1) {
                    let res = d.service.matchTopic(oldData, newData, 2);
                    if (res.labels.length > 0) {
                        d.service.sendAll(d.result, 'VerificationStatusChanged', res.labels, d.params);
                    }
                }
                if (name === 'STEAM' && newData && uid == 1) {
                    let res = d.service.matchTopic(oldData, newData, 1);
                    if (res.labels.length > 0) {
                        d.service.sendAll(d.result, 'VerificationStatusChanged', res.labels, d.params);
                    }
                }
                d.result = {};
            },
        ],
        remove: [
            async (d) => {
                const { uid, _id } = Acan.clone(d.result);
                // 统计学校下的学科数量
                d.app.service('curric').count({ school: uid, model: 'subjects' });
                //
                await d.service.sendAll(d.result, 'VerificationStatusChanged', [], d.params);
                // d.app.service('curric').countSubjects({school: uid, subject: _id, curriculum: []})
            },
        ],
    },
    error: {
        all: [],
        find: [],
        get: [],
        create: [],
        update: [],
        patch: [],
        remove: [],
    },
};
