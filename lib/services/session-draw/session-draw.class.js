"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SessionDraw = void 0;
const feathers_mongoose_1 = require("feathers-mongoose");
class SessionDraw extends feathers_mongoose_1.Service {
    //eslint-disable-next-line @typescript-eslint/no-unused-vars
    constructor(options, app) {
        super(options);
        this.app = app;
    }
    async getByPageId({ sid, pageId }, params) {
        const rs = await this.Model.findOne({ sid, pageId });
        if (rs)
            return rs;
        return await this.create({ sid, pageId });
    }
}
exports.SessionDraw = SessionDraw;
