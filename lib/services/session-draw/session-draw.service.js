"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const session_draw_class_1 = require("./session-draw.class");
const session_draw_model_1 = __importDefault(require("../../models/session-draw.model"));
const session_draw_hooks_1 = __importDefault(require("./session-draw.hooks"));
function default_1(app) {
    const options = {
        Model: (0, session_draw_model_1.default)(app),
        paginate: app.get('paginate')
    };
    // Initialize our service with any options it requires
    app.use('/session-draw', new session_draw_class_1.SessionDraw(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('session-draw');
    service.hooks(session_draw_hooks_1.default);
}
exports.default = default_1;
