"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ToolData = void 0;
const feathers_mongoose_1 = require("feathers-mongoose");
const logger_1 = __importDefault(require("../../logger"));
class ToolData extends feathers_mongoose_1.Service {
    //eslint-disable-next-line @typescript-eslint/no-unused-vars
    constructor(options, app) {
        super(options);
        this.app = app;
    }
    // patch all members session data
    // App.service('tool-data').patch('all', {'data.key': {val: '123', ...}}, {query: {session: 'session._id'}})
    async patchAll(post, params) {
        var _a;
        const { session, assessor } = (_a = params.query) !== null && _a !== void 0 ? _a : {};
        return await this.Model.updateMany({ session, assessor }, { $set: post });
    }
    async ext(result, params) {
        result.owner = await this.app.service('users').uidToInfo(result.assessor);
        return result;
    }
    // auto update session toolStat after tool-data patch filled
    // tool-data数据变化后，自动更新session.toolStat
    async getToolStat({ sid }) {
        logger_1.default.warn('getToolStat: ', sid);
        const sessionModel = this.app.service('session').Model;
        const toolDataSelect = ['tool', 'session', 'assessor', 'student'];
        const toolDatas = Acan.clone(await this.Model.find({ filled: true, session: sid }).select(toolDataSelect));
        const toolStat = { self: 0, teacher: 0, others: 0 };
        // 获取父session
        const sessionSelect = ['pid', 'students', 'regNum', 'reg._id'];
        let session = await sessionModel.findOne({ sid }).select(sessionSelect);
        if (session.pid)
            session = await sessionModel.findById(session.pid).select(sessionSelect);
        if (session.pid)
            session = await sessionModel.findById(session.pid).select(sessionSelect);
        const memberCount = session.students.length || session.regNum;
        const memberIds = !Acan.isEmpty(session.reg) ? session.reg.map((v) => v._id) : session.students;
        memberIds.map((v) => {
            toolStat[v] = { self: 0, teacher: 0, others: 0 };
        });
        logger_1.default.warn('getToolStat: ', memberIds);
        // 每个学生的统计
        const or = memberCount > 2 ? 0.5 : 1; // 他评统计的人数定义
        let others = 0;
        for (const uid of memberIds) {
            for (const v of toolDatas.filter((v) => [uid, 'teacher'].includes(v.assessor))) {
                if (v.assessor === 'teacher')
                    toolStat[uid].teacher += 1;
                else if (v.assessor === v.student)
                    toolStat[uid].self += 1;
                else if (v.assessor !== v.student)
                    toolStat[uid].others += 1;
            }
        }
        // 统计他评数量
        for (const uid in toolStat) {
            if (toolStat[uid].others * or >= 1)
                others++;
        }
        toolStat.others = others;
        // 整个课堂的统计
        for (const v of toolDatas) {
            if (v.assessor === 'teacher')
                toolStat.teacher += 1;
            else if (v.assessor === v.student)
                toolStat.self += 1;
            // else if (v.assessor !== v.student) toolStat.others += or
        }
        return await sessionModel.updateOne({ sid }, { $set: { toolStat } });
    }
}
exports.ToolData = ToolData;
