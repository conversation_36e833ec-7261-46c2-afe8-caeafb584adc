"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const school_year_class_1 = require("./school-year.class");
const school_year_model_1 = __importDefault(require("../../models/school-year.model"));
const school_year_hooks_1 = __importDefault(require("./school-year.hooks"));
function default_1(app) {
    const options = {
        Model: (0, school_year_model_1.default)(app),
        paginate: app.get('paginate')
    };
    // Initialize our service with any options it requires
    app.use('/school-year', new school_year_class_1.SchoolYear(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('school-year');
    service.hooks(school_year_hooks_1.default);
}
exports.default = default_1;
