"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const service_pack_user_class_1 = require("./service-pack-user.class");
const service_pack_user_model_1 = __importDefault(require("../../models/service-pack-user.model"));
const service_pack_user_hooks_1 = __importDefault(require("./service-pack-user.hooks"));
function default_1(app) {
    const options = {
        Model: (0, service_pack_user_model_1.default)(app),
        whitelist: ['$exists', '$regex', '$options'],
        paginate: app.get('paginate'),
    };
    // Initialize our service with any options it requires
    app.use('/service-pack-user', new service_pack_user_class_1.ServicePackUser(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('service-pack-user');
    service.hooks(service_pack_user_hooks_1.default);
}
exports.default = default_1;
