"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const authentication = __importStar(require("@feathersjs/authentication"));
const hook_1 = __importDefault(require("../../hook"));
// Don't remove this comment. It's needed to format import lines nicely.
const { authenticate } = authentication.hooks;
exports.default = {
    before: {
        all: [authenticate('jwt')],
        find: [
            (d) => {
                var _a;
                const query = (_a = d.params.query) !== null && _a !== void 0 ? _a : {};
                if (!query.rid)
                    hook_1.default.sysQuery()(d); // 没有 rid 的时候，限制只能看自己的
            },
        ],
        get: [],
        create: [
            (d) => {
                var _a;
                d.data.uid = (_a = d.params.user) === null || _a === void 0 ? void 0 : _a._id;
            },
        ],
        update: [],
        patch: [],
        remove: [],
    },
    after: {
        all: [],
        find: [
            async (d) => {
                if (d.result) {
                    for (let i = 0; i < d.result.data.length; i++) {
                        if (d.result.data[i].uid)
                            await d.service.extUser(d.result.data[i]);
                    }
                }
            },
        ],
        get: [],
        create: [],
        update: [hook_1.default.disable],
        patch: [],
        remove: [],
    },
    error: {
        all: [],
        find: [],
        get: [],
        create: [],
        update: [],
        patch: [],
        remove: [],
    },
};
