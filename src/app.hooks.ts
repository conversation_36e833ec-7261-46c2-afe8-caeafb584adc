// Application hooks that run for every service
// Don't remove this comment. It's needed to format import lines nicely.

import logger from './logger'

export default {
  before: {
    all: [
      (d: any) => {
        d.params._startTime = Date.now()
      },
    ],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: [],
  },

  after: {
    all: [
      (d: any) => {
        logger.log(d.path, d.method, d.id ?? '', Date.now() - d.params._startTime + 'ms')
      },
      async (d: any) => {
        if (d.params?.mongoose?.session) {
          // Override toJSON method to exclude session during serialization
          // but keep the session object intact for database operations
          if (!d.params.mongoose.toJSON) {
            d.params.mongoose.toJSON = function () {
              const {session, ...rest} = this
              return rest
            }
          }
        }
        return d
      },
    ],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: [],
  },

  error: {
    all: [
      (d: any) => {
        const {_id, nickname, email} = d.params.user ?? {}
        if (!d.error) return // 被重写成正常返回
        if (d.error.code === 404) return // ignore
        if (['jwt expired'].includes(d.error.message)) return // ignore
        const post = {
          ip: d.params.Aip || global.LocalIp,
          ttl: Date.now() - d.params._startTime,
          method: d.method,
          pathname: d.path,
          uid: _id,
          nickname,
          email,
          type: ['node', d.path, d.method, d.id, d.error.hook?.type, d.error.name].join('.'),
          status: d.error.code,
          stack: d.error.stack,
          ua: d.params.headers?.['user-agent'] ?? '',
          body: {data: d.data, query: d.params.query},
          msg: d.error.message || '',
        }
        d.app.service('log').create(post).then()
        // logger.warn(post)
      },
      async (d: any) => {
        if (d.params?.mongoose?.session) {
          // Override toJSON method to exclude session during serialization
          // but keep the session object intact for database operations
          if (!d.params.mongoose.toJSON) {
            d.params.mongoose.toJSON = function () {
              const {session, ...rest} = this
              return rest
            }
          }
        }
        return d
      },
    ],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: [],
  },
}
