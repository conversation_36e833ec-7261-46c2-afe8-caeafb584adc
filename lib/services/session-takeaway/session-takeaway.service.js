"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const session_takeaway_class_1 = require("./session-takeaway.class");
const session_takeaway_model_1 = __importDefault(require("../../models/session-takeaway.model"));
const session_takeaway_hooks_1 = __importDefault(require("./session-takeaway.hooks"));
function default_1(app) {
    const options = {
        Model: (0, session_takeaway_model_1.default)(app),
        paginate: app.get('paginate')
    };
    // Initialize our service with any options it requires
    app.use('/session-takeaway', new session_takeaway_class_1.SessionTakeaway(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('session-takeaway');
    service.hooks(session_takeaway_hooks_1.default);
}
exports.default = default_1;
