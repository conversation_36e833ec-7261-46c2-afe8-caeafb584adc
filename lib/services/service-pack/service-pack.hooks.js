"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const hook_1 = __importDefault(require("../../hook"));
const authentication = __importStar(require("@feathersjs/authentication"));
const { authenticate } = authentication.hooks;
exports.default = {
    before: {
        all: [],
        find: [
            (d) => {
                var _a, _b;
                const query = (_a = d.params.query) !== null && _a !== void 0 ? _a : {};
                // 分词模糊匹配 https://github.com/zran-nz/bug/issues/5069
                if ((_b = query.name) === null || _b === void 0 ? void 0 : _b.$regex) {
                    const reg = Acan.clone(query.name);
                    reg.$regex = reg.$regex.replace(/\s/g, '|');
                    query.$or = [{ name: reg }, { points: reg }, { keywords: reg }];
                    delete query.name;
                }
                if (query.$school) {
                    d.params.$school = query.$school;
                    delete query.$school;
                }
            },
        ],
        get: [hook_1.default.toClass],
        create: [
            authenticate('jwt'),
            hook_1.default.managerRoleFilter(['admin', 'academic_consultant', 'sales_manager', 'sales', 'customer_service_manager', 'customer_service', 'accountant']),
            (d) => {
                delete d.data.count;
            },
        ],
        update: [hook_1.default.disable],
        patch: [
            authenticate('jwt'),
            hook_1.default.managerRoleFilter(['admin', 'academic_consultant', 'sales_manager', 'sales', 'customer_service_manager', 'customer_service', 'accountant']),
            (d) => {
                delete d.data.count;
                if (d.data.status === true) {
                    d.data.lastPublished = new Date();
                }
            },
        ],
        remove: [
            authenticate('jwt'),
            hook_1.default.managerRoleFilter(['admin', 'academic_consultant', 'sales_manager', 'sales', 'customer_service_manager', 'customer_service', 'accountant']),
        ],
    },
    after: {
        all: [],
        find: [
            async (d) => {
                for (const one of d.result.data) {
                    d.service.checkDiscount(one);
                }
                if (isDev)
                    d.result.query = d.params.query;
                // 查询service-pack-school-price 用于buy按钮显示
                if (d.params.$school) {
                    for (let i = 0; i < d.result.data.length; i++) {
                        await d.service.extSchoolPrice(d.result.data[i], d.params.$school);
                    }
                }
                return d;
            },
        ],
        get: [
            (d) => {
                if (hook_1.default.classExist(d))
                    return d;
                d.service.checkDiscount(d.result);
                if (isDev && !Array.isArray(d.result))
                    d.result.query = d.params.query;
                return d;
            },
        ],
        create: [],
        update: [],
        patch: [
            async (d) => {
                // 面试服务包 & 管家服务包 & 1v1mentor，在点击unpublish时
                // 如果对应的主题商品正在售卖中，则自动下架，且系统从相应的主题商品中自动启动解绑该服务包，如果解绑的为面试服务包， https://github.com/zran-nz/bug/issues/5359
                if (d.data.status === false) {
                    await d.service.unBindPack(Acan.clone(d.result));
                }
            },
        ],
        remove: [
            async (d) => {
                var _a;
                let { _id, type, mentoringType, countryCode, curriculum, gradeGroup } = d.result;
                let query = { type, status: { $in: [1, 2, -1] } };
                if (mentoringType) {
                    query.mentoringType = mentoringType;
                }
                if ((countryCode === null || countryCode === void 0 ? void 0 : countryCode.length) === 1) {
                    query.countryCode = countryCode[0];
                }
                if (curriculum) {
                    query.curriculum = curriculum;
                }
                let auth = await d.app.service('service-auth').Model.find(query);
                let mailedUid = [];
                for (let i = 0; i < auth.length; i++) {
                    const item = auth[i];
                    if (mailedUid.includes(item.uid)) {
                        continue;
                    }
                    let overlap = Acan.intersection(gradeGroup, item.gradeGroup);
                    if (overlap.length > 0) {
                        mailedUid.push(item.uid);
                        let user = await d.app.service('users').uidToInfo(item.uid);
                        let name = await d.app.service('service-auth').getAuthName(item);
                        name.reverse().push(...item.gradeGroup);
                        name = name.map((e) => `# ${e}`);
                        await d.app
                            .service('notice-tpl')
                            .mailto('ServicePackageTerminated', user.email, { username: user.nickname, name: name.join(' ') }, (_a = d.params.user) === null || _a === void 0 ? void 0 : _a._id);
                    }
                }
                // 删除相应数据service-pack-school-price
                await d.app.service('service-pack-school-price').Model.deleteMany({ servicePack: _id.toString() });
            },
        ],
    },
    error: {
        all: [],
        find: [],
        get: [],
        create: [],
        update: [],
        patch: [],
        remove: [],
    },
};
