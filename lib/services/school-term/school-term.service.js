"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const school_term_class_1 = require("./school-term.class");
const school_term_model_1 = __importDefault(require("../../models/school-term.model"));
const school_term_hooks_1 = __importDefault(require("./school-term.hooks"));
function default_1(app) {
    const options = {
        Model: (0, school_term_model_1.default)(app),
        multi: ['remove'],
        paginate: app.get('paginate'),
    };
    // Initialize our service with any options it requires
    app.use('/school-term', new school_term_class_1.SchoolTerm(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('school-term');
    service.hooks(school_term_hooks_1.default);
}
exports.default = default_1;
