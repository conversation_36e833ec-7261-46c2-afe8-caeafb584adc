import {Id, NullableId, Paginated, Params, ServiceMethods} from '@feathersjs/feathers'
import {Application} from '../../declarations'
const {NotFound, GeneralError, BadRequest} = require('@feathersjs/errors')
import axios from 'axios'
const paypalSettings = require('../../../config/paypal.json')
const paypalSetting = !isDev ? paypalSettings.live : paypalSettings.sandbox
import {ObjectID} from 'bson'

interface Data {}

interface ServiceOptions {}

export class Paypal implements ServiceMethods<Data> {
  app: Application
  options: ServiceOptions

  constructor(options: ServiceOptions = {}, app: Application) {
    this.options = options
    this.app = app
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find(params?: Params): Promise<Data[] | Paginated<Data>> {
    return []
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get(id: Id, params?: Params): Promise<Data> {
    return {
      id,
      text: `A new message with ID: ${id}!`,
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create(data: Data, params?: Params): Promise<Data> {
    if (Array.isArray(data)) {
      return Promise.all(data.map((current) => this.create(current, params)))
    }

    return data
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update(id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch(id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove(id: NullableId, params?: Params): Promise<Data> {
    return {id}
  }

  // Return order id and Pull up the payment desk
  // TODO gift card
  async getPayment({id}: {id: string}, params: Params): Promise<Data> {
    const order: any = await this.app.service('order').Model.findOne({_id: id})

    // 检查主题服务包
    // if (checkPremium && order.type == 'service_premium') {
    //   let servicePack: any = await this.app.service('service-pack').Model.findOne({_id: order.servicePremium})
    //   if (!servicePack || new Date(servicePack.updatedAt).getTime() != new Date(order.servicePremiumSnapshot.updatedAt).getTime()) {
    //     return Promise.reject(new BadRequest('Service pack is changed'))
    //   }
    // }

    let links = order.links.filter((item: any) => !item.removed)
    if (links.length === 0) {
      return Promise.reject(new GeneralError('Links is empty'))
    }
    if (order.status !== 100) {
      return Promise.reject(new GeneralError('Order status is not 100'))
    }
    let res = await this.createPaypalOrder(order.price, order._id)
    if (res.status === 'CREATED') {
      return {
        success: true,
        ...res,
      }
    } else {
      return Promise.reject(new GeneralError('Paypal created err'))
    }
  }

  // Get paypal access token
  async getRequestToken() {
    const authorization = Buffer.from(`${paypalSetting.client_id}:${paypalSetting.client_secret}`).toString('base64')
    const redis = this.app.get('redis')
    let token = await redis.get('paypal_token')
    if (token) {
      return token
    }

    return new Promise((resolve, reject) => {
      axios({
        method: 'post',
        url: `${paypalSetting.host}/v1/oauth2/token?grant_type=client_credentials`,
        headers: {
          Accept: 'application/json',
          'Accept-Language': 'en_US',
          Authorization: 'Basic ' + authorization,
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      })
        .then(async (res) => {
          let {access_token, expires_in} = res.data
          await redis.set('paypal_token', access_token)
          await redis.EXPIRE('paypal_token', expires_in - 100)
          resolve(access_token)
        })
        .catch(function (err) {
          reject(err.response.data.error_description)
        })
    })
  }

  // Create paypal order
  async createPaypalOrder(amount: string, invoice_id: string): Promise<any> {
    let token = await this.getRequestToken()
    let price = (parseFloat(amount) / 100).toFixed(2)
    return axios({
      method: 'post',
      url: `${paypalSetting.host}/v2/checkout/orders`,
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
      data: {
        purchase_units: [
          {
            amount: {
              currency_code: 'USD',
              value: price,
              // breakdown: {
              //   item_total: {
              //     currency_code: 'USD',
              //     value: amount,
              //   },
              //   // discount: {
              //   //   currency_code: 'USD',
              //   //   value: '20',
              //   // },
              // },
            },
            // items: [
            //   {
            //     name: 'class',
            //     quantity: '1',
            //     unit_amount: {
            //       currency_code: 'USD',
            //       value: amount,
            //     },
            //   },
            // ],
            invoice_id: invoice_id,
            // custom_id: invoice_id,
            // reference_id: invoice_id,
            description: 'full payment',
          },
        ],
        intent: 'CAPTURE',
      },
    })
      .then((res) => {
        return res.data
      })
      .catch((err) => {
        return err.response.data
      })
  }

  // Paypal refund
  async getRefund({id, amount}: any, params: Params): Promise<any> {
    try {
      let result: any = await this.paypalRefundRequest(id, amount)
      if (result.status === 'COMPLETED') {
        return {
          success: true,
          message: 'refund succeeded',
        }
      } else {
        return {
          success: false,
          message: 'refund failed',
        }
      }
    } catch (err) {
      return {
        success: false,
        message: err,
      }
    }
  }

  // Paypal refund request
  async paypalRefundRequest(capture_id: string, amount: string) {
    let token = await this.getRequestToken()
    return new Promise((resolve, reject) => {
      axios({
        method: 'post',
        url: `${paypalSetting.host}/v2/payments/captures/${capture_id}/refund`,
        headers: {'Content-Type': 'application/json', Authorization: 'Bearer ' + token},
        data: JSON.stringify({
          amount: {value: amount, currency_code: 'USD'},
        }),
      })
        .then((res) => {
          resolve(res.data)
        })
        .catch((err) => {
          reject(err.response.data.message)
        })
    })
  }
  // Show captured payment details
  async paypalCapturesRequest(capture_id: string) {
    let token = await this.getRequestToken()
    return new Promise((resolve, reject) => {
      axios({
        method: 'get',
        url: `${paypalSetting.host}/v2/payments/captures/${capture_id}`,
        headers: {'Content-Type': 'application/json', Authorization: 'Bearer ' + token},
      })
        .then((res) => {
          resolve(res.data)
        })
        .catch((err) => {
          reject(err.response.data.message)
        })
    })
  }
  // Show order details
  async paypalOrderRequest(id: string) {
    let token = await this.getRequestToken()
    return new Promise((resolve, reject) => {
      axios({
        method: 'get',
        url: `${paypalSetting.host}/v2/checkout/orders/${id}`,
        headers: {'Content-Type': 'application/json', Authorization: 'Bearer ' + token},
      })
        .then((res) => {
          resolve(res.data)
        })
        .catch((err) => {
          reject(err.response.data.message)
        })
    })
  }

  // Check if refund already exists for given capture id
  async checkRefundExists(capture_id: string): Promise<{exists: boolean}> {
    try {
      const captureDetails: any = await this.paypalCapturesRequest(capture_id)
      return {exists: captureDetails.status && ['REFUNDED', 'PARTIALLY_REFUNDED'].includes(captureDetails.status)}
    } catch (error) {
      console.error('Error checking PayPal refund status:', error)
      // If we can't check, assume refund doesn't exist to be safe
      return {exists: false}
    }
  }

  async verifyPayPalWebhookSignature(headers: any, body: any): Promise<boolean> {
    try {
      // Extract required headers
      const authAlgo = headers['paypal-auth-algo']
      const certUrl = headers['paypal-cert-url']
      const transmissionId = headers['paypal-transmission-id']
      const transmissionSig = headers['paypal-transmission-sig']
      const transmissionTime = headers['paypal-transmission-time']

      // Validate required headers are present
      if (!authAlgo || !certUrl || !transmissionId || !transmissionSig || !transmissionTime) {
        console.error('Missing required PayPal webhook headers')
        return false
      }

      // Get access token
      const token = await this.getRequestToken()

      // Prepare verification request
      const verificationData = {
        auth_algo: authAlgo,
        cert_url: certUrl,
        transmission_id: transmissionId,
        transmission_sig: transmissionSig,
        transmission_time: transmissionTime,
        webhook_id: paypalSetting.webhook_id,
        webhook_event: body,
      }

      // Call PayPal's verification endpoint
      const response = await axios({
        method: 'post',
        url: `${paypalSetting.host}/v1/notifications/verify-webhook-signature`,
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        data: verificationData,
      })
      console.log('response', response.data)
      // Check verification status
      return response.data.verification_status === 'SUCCESS'
    } catch (error: any) {
      console.error('Error verifying PayPal webhook signature:', error?.response?.data || error?.message)
      return false
    }
  }
}
