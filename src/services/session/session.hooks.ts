import {exec} from 'child_process'
import {HookContext} from '@feathersjs/feathers'
import * as authentication from '@feathersjs/authentication'
// Don't remove this comment. It's needed to format import lines nicely.

const {authenticate} = authentication.hooks
const search = require('feathers-mongodb-fuzzy-search')
import hook from '../../hook'
import logger from '../../logger'
import {BadRequest} from '@feathersjs/errors'
import {conditionalAwait, queueForCommit} from '../../dbTransactions'

function logQuery(query: any) {
  const logRs = Acan.clone(query)
  delete logRs.$select
  logger.info(logRs)
}

export default {
  before: {
    all: [],
    find: [
      hook.userQuery(),
      async (d: HookContext) => {
        const query: any = d.params.query ?? {}
        let $substitute = query.$substitute
        delete query.$substitute
        if (query.sid) return d
        query.del = query.del ?? false
        // if (!query.del) query.status = query.status ?? {$ne: 'close'}
        query.$select = Acan.clone(query.$select ?? d.service.selectList)
        if (d.params.inside) return
        const uid = d.params.user?._id
        if (query.type === 'courses') query.$select.push('overview', 'childs')
        if (Acan.isObjectId(query._id) || query._id?.$in) {
          delete query.status
          return d
        }
        query.$or = Array.isArray(query.$or) ? query.$or : []
        if (query['subjects.session']) {
          query.$or.push({'task.outline.outline.subjects': query['subjects.session']})
          query.$or.push({'task.outline.assess.subjects': query['subjects.session']})
          delete query['subjects.session']
        } else if (query['subjects.pd']) {
          query.$or.push({'task.outline.outline.subjects': query['subjects.pd']})
          query.$or.push({'task.outline.assess.subjects': query['subjects.pd']})
          // query.$or.push({'task.outline.pd.subjects': query['subjects.pd']})
          // query.$or.push({'task.outline.assess.subjects': query['subject.pd']})
          delete query['subjects.pd']
        }
        if (Acan.isEmpty(query.$or)) delete query.$or
        d.service.queryDate(query, d.params)
        // 公开的，匿名查询用
        if (query.isLib && !query.school) {
          // for library find
          Object.assign(query, {school: {$exists: false}})
          if (query.type !== 'selfStudy') {
            // 公共的自学习课堂,无需过滤时间
            delete query.start
            delete query.end
            delete query.uid
          } else if (query.type !== 'selfStudy') {
            // 非自学习课堂需要限制报名截至时间
            query.regDate = {$gt: new Date()}
          }
          // self-study not end
          // if (query.type === 'taskWorkshop' && query.sessionType === 'student') delete query.end
          if (query.sessionType === 'student') delete query.end
          delete query.isLib
          // if (uid) query.uid = {$ne: uid}
          if (query.pid) {
            // child find
            if (query.pid['$exists'] !== false) delete query.uid
          } else query.pid = {$exists: false}
          if (uid) {
            // new prompt 页面获取推广课数据，需要能获取自己或者别人的数据
            if (query.tab == 'other') {
              query.uid = {$ne: uid}
            } else if (query.tab === 'me') {
              query.uid = uid
            }
          }
          logger.info('session libary find', uid)
          logQuery(query)
          return
        }
        if (!uid) return
        // 查询和自己相关的
        // find self workshop
        if (['workshop', 'pdCourses'].includes(query.type) && uid) {
          if (!query.$select.includes('reg')) query.$select.push('reg')
        }
        // await authenticate('jwt')(d)
        if (query.$sys) {
          if (!hook.roleHas(['sys', 'admin'])(d)) return (d.result = null), d
          delete query.$sys
          query.$select.push('students')
          // if (query.uid === uid) delete query.uid
        } else {
          // View library, non-self data
          if (query.isLib) delete query.isLib //, query.uid = {$ne: uid}
          // view my register
          else if (query['reg._id']) {
            if (!hook.roleHas(['sys', 'admin'])(d)) {
              query['reg._id'] = uid
              delete query.del
            }
            // view my class session
          } else if (query.students) {
            if (!hook.roleHas(['sys', 'admin'])(d)) {
              query.students = uid
              // delete query.del
            }
          }
          // view my data
          else if (!query.school && !query.classId) {
            if (!hook.roleHas(['sys', 'admin'])(d) || !query.uid) {
              if (!$substitute) {
                query.uid = uid
              }
            }
          }
        }
        if (query.pid && query.pid['$exists'] !== false) delete query.uid
        logQuery(query)
      },
      search({
        fields: ['name'],
      }),
    ],
    get: [
      hook.toClass,
      async (d: HookContext) => {
        if (hook.classExist(d)) return d
        if (/^[\w\d]{5,10}$/.test(d.id + '')) {
          d.result = Acan.clone(await d.service.Model.findOne({sid: d.id}))
        }
        return d
      },
    ],
    create: [
      /** @transactional | `params` 可能包含一个 Mongoose session，所有数据库调用以及新添加的嵌套函数必须使用该 session。*/
      authenticate('jwt'),
      async (d: HookContext) => {
        const {booking, pid} = d.data
        const options = Acan.getTxnOptions(d.params)
        // booking 重复排课 https://github.com/zran-nz/bug/issues/6081
        // link 的课件不需要检查
        if (booking && !pid) {
          const rs = await d.app.service('service-booking').Model.findById(booking, null, options).select('session')
          if (rs.session?._id && !pid) return Promise.reject(new BadRequest('bookingHasImport', {booking, session: rs.session, _id: rs._id}))
        }
        // if (d.params.inside) return
        if (!d.data.regDate && d.data.sessionType === 'live' && Acan.isEmpty(d.data.students)) d.data.regDate = d.data.start
        let {id, cid, pages} = d.data
        if (!d.data.isAutoCreate) d.data.uid = d.params.user?._id
        if (d.data.childs) d.data.childSize = d.data.childs.length
        // live课自动设置end时间
        if ((!d.data.status || d.data.status === 'live') && !d.data.end) d.data.end = new Date(Date.now() + 45 * 60000)
        // load pages form unit
        // related get slides.id
        if (cid && Acan.isEmpty(id)) {
          const unitDoc = await d.app.service('unit').Model.findById(cid, null, options).select(['sid']) // , 'snapshot.pages'
          // if (Acan.isEmpty(pages)) pages = unitDoc.snapshot?.pages
          if (unitDoc.sid) id = unitDoc.sid
        }
        if (pages) {
          if (!d.data.task) d.data.task = {pages}
          else d.data.task.pages = pages
        }

        // task snapshot
        const {school, classId} = d.data
        if (cid) {
          d.data.task = await d.app.service('unit').snapshot({_id: cid, school, classId, session: true}, d.params)
          for (const key of ['questions', 'materials']) {
            delete d.data.task[key]
          }
        }
        if (d.data.type.includes('video')) {
          // 获取video快照数据
          if (!d.data.task.video) return (d.result = {message: `Not find video, task: ${cid}, video:${d.data.task.video}`}), d
          d.data.video = await d.app.service('interactive-videoes').Model.findById(d.data.task.video, null, options)
        } else if (id && !id.includes(':') && id !== 'disable') {
          // 获取ppt数据 load slides pages data, 排除购买和复制的课件
          let rs = await d.app.service('slides').Model.findOne({id}, null, options).select(['pages', 'rev'])
          if (!rs) {
            if (!d.data.guest || Acan.isEmpty(d.data.pageIds)) return (d.result = {message: `Not find slides, ${id}`}), d
            rs = await d.app.service('slides').create({id, pages: [{_id: d.data.pageIds[0], url: d.data.image}]}, Acan.mergeTxnParams(d.params))
          }
          if (d.data.task?.pages) d.data.task.pages = rs.pages || []
          else d.data.task = {pages: rs.pages || []}
          d.data.rev = rs.rev
          delete d.data.pageIds
        }
        // if (d.data.classId) {
        //   const [rs] = await knexj('cc_school_class').where({id: d.data.classId}).limit(1).select('name', 'school_id')
        //   if (rs) {
        //     d.data.className = rs.name
        //     if (rs.school_id !== '0') d.data.school = rs.school_id
        //     // todo 广播站内信给班级的成员
        //   }
        // }
        d.data.pageNum = d.data.pages?.length ?? 0
        if (!d.data.image) d.data.image = d.data.pages?.[0]?.url
        if (!d.data.sid) d.data.sid = 'C00' + Acan.base62encode(Date.now() % 10000000000)
        if (d.data.zoom) {
          const rs = await d.app.service('zoom-meet').create({sid: d.data.sid, start: d.data.start, name: d.data.name, ...d.data.zoom}, d.params)
          if (rs) d.data.zoom = rs
          // else delete d.data.zoom
        }
        // self-study session, need archived historically, conflict with create new
        // const {type, status, sessionType} = d.data
        // if (type === 'taskWorkshop' && status === 'student' && sessionType === 'student') {
        //   await d.service.Model.updateMany({cid: d.data.cid, type, status, sessionType, del: false}, {$set: {del: true}})
        // }
      },
    ],
    update: [hook.disable],
    patch: [
      /** @transactional | `params` 可能包含一个 Mongoose session，所有数据库调用以及新添加的嵌套函数必须使用该 session。*/
      authenticate('jwt'),
      hook.toClass,
      async (d: HookContext) => {
        const options = Acan.getTxnOptions(d.params)
        if (hook.classExist(d)) return d
        const {guest, sid, status} = d.data
        // 自动加上结束课堂的时间
        if (status === 'close') d.data.ended = new Date()
        if (d.params.inside) return d
        // 当课堂已经进入过学生的时候，不能开启匿名登录
        if (Acan.isDefined(guest) && sid) {
          const room = await d.app.service('rooms').Model.findOne({sid}, null, options).select(['members._id'])
          if (!Acan.isEmpty(room.members)) delete d.data.guest
          delete d.data.sid
        }
        // 检查报名人数是否超额
        // check regMax for enroll
        if (d.data?.$addToSet?.reg) {
          const rs = await d.service.Model.findOne({_id: d.id}, null, options).select(['regMax', 'reg'])
          if (rs.regMax && rs.regMax < rs.reg.length) {
            return Promise.reject(new Error('Registration is full'))
          }
        }

        // substitute
        let origin = await d.service.Model.findOne({_id: d.id}, null, options).lean()
        d.params.$origin = origin
        let {substituteTeacherStatus, substituteTeacher, substitutePackUser} = d.data
        let cancelBy = d.service.getSubstituteCancelBy({result: origin}, d.params)

        if (substituteTeacherStatus == 1 || substituteTeacher) {
          d.data.substituteOperateAt = new Date()
        }

        if (substituteTeacher && !origin.substituteWithin && origin?.substituteServicePackSnapshot?.isOnCampus) {
          // 校外线下抢单成功
          let isCompensation = await d.service.getIsCompensation({session: d.id, substituteTeacher}, d.params)
          if (!isCompensation) {
            d.data.substituteNoCompensation = true
          }
        }
        if (substitutePackUser) {
          let packUser = await d.app.service('service-pack-user').Model.findOne({_id: substitutePackUser}, null, options).lean()
          d.data.substituteServicePackSnapshot = packUser.snapshot
          d.data.substituteServicePackUserSnapshot = packUser
          // 服务包扣费
          let serviceResult = await d.service.substituteServiceUse({doc: origin, packUser, substituteNoCompensation: d.data.substituteNoCompensation}, d.params)
          if (!serviceResult.success) {
            return Promise.reject(new Error(serviceResult.message))
          }
          d.data.substituteDuration = serviceResult.duration
        }
        if (cancelBy === 'admin' && !origin.substituteWithin && d.data?.$unset?.hasOwnProperty('substituteTeacher')) {
          // 更换老师=管理员 校外 取消已匹配老师
          d.service.substituteSend(origin, 'CancellationOfSubstituteService', d.params)
        }
        if (cancelBy === 'teacher' && !origin.substituteWithin && origin.substituteTeacherStatus == 1 && d.data?.$unset?.hasOwnProperty('substituteTeacher')) {
          // 代课老师取消
          d.service.substituteSend(origin, 'TerminationOfSubstituteServiceByServiceProvider(To admin)', d.params)
          // 2小时内 取消代课 退还服务包时间
          d.service.handleCancelByTeacher({doc: origin}, d.params)
        }
        // 校外 取消代课
        if (
          (cancelBy === 'admin' || cancelBy === 'owner') &&
          !origin.substituteWithin &&
          origin.substitutePackUser &&
          d.data?.$unset?.hasOwnProperty('substitutePackUser')
        ) {
          d.service.handleServicePackReturn({doc: origin}, d.params)
        }
        // 开课时间重排 退服务包课时长 取消代课
        if (d.data?.start) {
          if (!origin.substituteWithin && origin.substitutePackUser) {
            // 校外已预约退服务包
            await d.service.handleServicePackReturn({doc: origin}, d.params)
          }
          d.data.$unset = Object.assign(d.data.$unset ?? {}, {
            substituteWithin: '',
            substituteTeacher: '',
            substituteTeacherStatus: '',
            substituteTeacherMessage: '',
            substituteAdmin: '',
            substituteAdminStatus: '',
            substituteAdminMessage: '',
            substitutePackUser: '',
            substituteServicePackSnapshot: '',
            substituteServicePackUserSnapshot: '',
            substituteSubject: '',
            substituteTopic: '',
            substitutePush: '',
            substituteDuration: '',
            substitutePushTime: '',
            substitutePushAll: '',
            substitutePriorityPush: '',
            substituteReminder: '',
            substituteNoCompensation: '',
            zoom: '',
          })
        }
        if (origin.substituteTeacherStatus == 1 && d.data?.$unset?.hasOwnProperty('substituteTeacher')) {
          await d.service.Model.updateOne({_id: d.id}, {$addToSet: {substituteExclude: origin.substituteTeacher}}, options)
        }
        // zoom
        if (d.data.zoom) {
          const rs = await d.app.service('zoom-meet').create({sid: origin.sid, start: origin.start, name: origin.name, ...d.data.zoom}, d.params)
          if (rs) d.data.zoom = rs
          else delete d.data.zoom
        }
      },
    ],
    remove: [
      /** @transactional | `params` 可能包含一个 Mongoose session，所有数据库调用以及新添加的嵌套函数必须使用该 session。*/
      authenticate('jwt'),
      (d: HookContext) => {
        if (hook.roleHas(['sys'])(d)) return d
        // 老师只能删除自己排的课
        // 学生只能删除自己预约的课 https://github.com/zran-nz/bug/issues/4973
        const uid = d.params.user?._id
        const query = {
          $or: [{uid}, {students: uid, booking: {$exists: true}}],
        }
        Object.assign(d.params.query ?? {}, query)
      },
    ],
  },

  after: {
    all: [],
    find: [
      async (d: HookContext) => {
        if (!d.result?.data) return
        for (const one of d.result.data) {
          await d.service.ext(one, d.params)
          await d.service.extService(one, d.params)
          await d.service.extRating(d.result, d.params)
        }
        await d.service.extBooking(d.result.data, d.params)
        if (isDev) {
          d.result.query = d.params.query
          d.result.ext = {uid: d.params.user?._id}
        }
      },
    ],
    get: [
      async (d: HookContext) => {
        if (hook.classExist(d)) return d
        if (!d.result?._id) return d
        // await d.service.extTask(d.result)
        // await d.service.extSlides(d.result)
        await d.service.extSnapshot(d.result)
        await d.service.ext(d.result, d.params)
        await d.service.extService(d.result, d.params)
        await d.service.extRating(d.result, d.params)
      },
    ],
    create: [
      /** @transactional | `params` 可能包含一个 Mongoose session，所有数据库调用以及新添加的嵌套函数必须使用该 session。*/
      async (d: HookContext) => {
        if (!d.result?._id) return
        const {_id, sid, block, students} = d.result
        d.service.sendZoomNotice(d.result, d.params)
        const options = Acan.getTxnOptions(d.params)
        // 创建 rooms, 并且处理 block 数据 https://github.com/zran-nz/bug/issues/3238
        const roomsPost: any = {sid}
        if (block && !Acan.isEmpty(students)) roomsPost.block = students
        await d.app.service('rooms').Model.create([roomsPost], options)

        if (d.data.childs && _id) {
          const childs = d.data.childs.map((v: any) => v._id)
          await d.service.Model.updateMany({_id: {$in: childs}, pid: {$exists: false}}, {$set: {pid: _id}}, options)
        }
        // 老师对预约进行排课后，更新 service-booking.session
        if (d.result.booking && _id && !d.data.pid) {
          const {name, image, status, booking} = d.result
          await d.app.service('service-booking').patch(
            booking,
            {
              session: {
                _id,
                name,
                image,
                status,
              },
            },
            Acan.mergeTxnParams(d.params)
          )
          if (!!options.session) {
            // if under transaction, need to use await
            try {
              const tutorialPackUser = await d.app.service('service-booking').getTutorialPackId({_id: booking}, Acan.mergeTxnParams(d.params))
              await d.service.Model.updateOne({_id}, {$set: {tutorialPackUser}}, options)
            } catch (e: any) {
              // 找不到辅导包
              logger.info(e.message)
            }
          } else {
            d.app
              .service('service-booking')
              .getTutorialPackId({_id: booking})
              .then((tutorialPackUser: String) => {
                d.service.Model.updateOne({_id}, {$set: {tutorialPackUser}}).then()
              })
              .catch((e: any) => {
                // 找不到辅导包
                logger.info(e.message)
              })
          }
        }
      },
    ],
    update: [],
    patch: [
      /** @transactional | `params` 可能包含一个 Mongoose session，所有数据库调用以及新添加的嵌套函数必须使用该 session。*/
      async (d: HookContext) => {
        if (hook.classExist(d)) return d
        const {pid, _id, sid, booking, premium} = d.result

        const options = Acan.getTxnOptions(d.params)

        if (booking && d.data.status === 'close') {
          queueForCommit(d.app, 'sales-follow-up', 'handleFollowUpUnpaid', [booking], d.params)
          // d.app.service('sales-follow-up').handleFollowUpUnpaid(booking)
          d.service.sendEndNotice(d.result, d.params)
        }
        // 课堂结束 关闭未付费prompt订单
        if (d.data.status === 'close') {
          d.app
            .service('order')
            .Model.updateMany({status: 100, links: {$elemMatch: {style: 'prompt', sessionId: _id.toString()}}}, {status: 400}, options)
            .exec()
        }
        // 1v1捆绑精品lecture end
        if (d.data.status === 'close') {
          d.service.sendPremiumNotice(d.result, d.params)
        }

        // substitute
        let origin = d.params.$origin ?? {}
        let cancelBy = d.service.getSubstituteCancelBy({result: origin}, d.params)
        let {substituteWithin} = d.result
        let {substituteTeacher, substituteAdmin, substituteAdminStatus} = d.data
        // 校内 邀请代课老师
        if (substituteWithin && substituteTeacher) {
          d.service.substituteSend(d.result, 'InvitedForSubstituteTeaching', d.params)
        }
        // 校内 代课老师拒绝
        if (origin.substituteWithin && cancelBy === 'teacher' && d.data?.$unset?.hasOwnProperty('substituteTeacher')) {
          d.service.substituteSend(d.result, 'SubstituteInvitationRejected', d.params)
        }
        // 校外 老师邀请管理员
        if (!substituteWithin && substituteAdmin) {
          d.service.substituteSend(d.result, 'RequestForSubstituteTeacher', d.params)
        }

        // 校外 管理员取消
        if (!origin.substituteWithin && cancelBy === 'admin' && d.data?.$unset?.hasOwnProperty('substituteAdmin')) {
          if (origin.substituteTeacher) {
            d.service.substituteSend(origin, 'CancellationOfSubstituteService', d.params)
          }
          d.service.substituteSend(origin, 'RejectOfSubstituteTeachingRequest', d.params)
          d.service.sendCancelNotice({doc: origin}, d.params)
        }
        // 校外 发起人取消
        if (!origin.substituteWithin && cancelBy === 'owner' && d.data?.$unset?.hasOwnProperty('substituteAdmin')) {
          if (origin.substituteTeacher) {
            d.service.substituteSend(origin, 'CancellationOfSubstituteService', d.params)
          }
          if (origin.substituteAdminStatus == 1) {
            d.service.substituteSend(origin, 'CancellationOfSubstituteService(97)', d.params)
          }
          d.service.sendCancelNotice({doc: origin}, d.params)
        }
        if (substituteAdminStatus == 1) {
          d.service.substituteSend(d.result, 'ApprovalOfSubstituteTeachingRequest', d.params)
        }

        // 同步状态到预约数据中，更新 service-booking
        if (booking && d.data.status) {
          await d.app.service('service-booking').patch(booking, {'session.status': d.data.status}, Acan.mergeTxnParams(d.params))
        }
        // auto update parent status
        if (pid && d.data.status) {
          if (d.data.status === 'close') {
            d.service.endParent(pid, d.params)
            // clean student countdown
            await d.app.get('redis').DEL(d.app.service('rooms').countDownKey(sid))
          } else {
            await d.service.liveParent(d.result, d.params)
          }
        }
        if (d.data.$pull?.reg || d.data.$addToSet?.reg) {
          await d.app
            .service('session')
            .Model.updateOne({_id}, {$set: {regNum: d.result.reg?.length || 0}}, options)
            .then()
        }
        if (d.data.$addToSet?.reg) {
          await d.service.regNotice(d.data, d.params, d.result, d.data.$addToSet.reg)
        }
        if (d.data.$pull || d.data.$addToSet) {
          const rs = {_id: d.id}
          for (const key in d.data.$pull || d.data.$addToSet) {
            Acan.objSet(rs, key, Acan.objGet(d.result, key))
          }
          d.result = rs
        } else {
          d.result = {_id: d.id, sid: d.result.sid, ...d.data}
        }
      },
    ],
    remove: [
      /** @transactional | `params` 可能包含一个 Mongoose session，所有数据库调用以及新添加的嵌套函数必须使用该 session。*/
      async (d: HookContext) => {
        const options = Acan.getTxnOptions(d.params)
        const {_id, sid, booking, zoom, isAutoCreate, substituteAdmin, substituteAdminStatus, substituteTeacher} = d.result

        await d.app.service('order').getCancelByLinkId({linkId: _id.toString(), status: 501}, d.params)
        await conditionalAwait(d.service.removeRelated(d.result, d.params), !!options.session)

        if (sid) await d.app.service('rooms').Model.deleteOne({sid}, options)
        queueForCommit(d.app, 'zoom-meet', 'rmBySession', [zoom], d.params)
        // d.app.service('zoom-meet').rmBySession(zoom)
        await conditionalAwait(d.service.removeNotice(d.result, d.params), !!options.session)

        if (isAutoCreate && booking) {
          // 自动排课的需要同时取消预订
          await d.app.service('service-booking').patchCancel({_id: booking}, d.params)
        } else if (booking && _id) {
          // 老师取消课程后，移除 service-booking.session
          await d.app.service('service-booking').unBindSession(booking, d.params)
        }
        // premium_cloud认证精品课快照,订单使用状态更新
        await conditionalAwait(
          d.app
            .service('order')
            .Model.updateOne({'links.session': _id.toString()}, {'links.$.used': false, $unset: {'links.$.bookingId': '', 'links.$.session': ''}}, options)
            .exec(),
          !!options.session
        )
        // new prompt订单关闭
        await conditionalAwait(
          d.app
            .service('order')
            .Model.updateMany({status: 100, links: {$elemMatch: {style: 'prompt', sessionId: _id.toString()}}}, {status: 400}, options)
            .exec(),
          !!options.session
        )

        // todo refund

        // substitute
        if (substituteAdminStatus == 1) {
          await conditionalAwait(d.service.substituteSend(d.result, 'CancellationOfSubstituteService(97)', d.params), !!options.session)
        }
        if (substituteTeacher) {
          await conditionalAwait(d.service.substituteSend(d.result, 'CancellationOfSubstituteService', d.params), !!options.session)
        }
        if (!d.result.substituteWithin && d.result.substitutePackUser) {
          await conditionalAwait(d.service.handleServicePackReturn({doc: d.result}, d.params), !!options.session)
        }
      },
    ],
  },

  error: {
    all: [
      (d: HookContext) => {
        if (d.error.code === 404) return d
        logger.error(d.error)
      },
    ],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: [],
  },
}
