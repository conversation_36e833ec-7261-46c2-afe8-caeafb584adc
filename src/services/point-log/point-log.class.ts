import {Service, MongooseServiceOptions} from 'feathers-mongoose'
import {Application} from '../../declarations'
import {Params} from '@feathersjs/feathers'
import {TxnParams} from '../../dbTransactions'
const {NotFound, GeneralError, BadRequest} = require('@feathersjs/errors')

export class PointLog extends Service {
  app: Application
  //eslint-disable-next-line @typescript-eslint/no-unused-vars
  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.app = app
  }
  // 统计
  async getCount({}: any, params: Params): Promise<any> {
    const uid = params?.user?._id
    let expectedAmount = await this.Model.aggregate([
      {$match: {status: 0, tab: 'earn', uid}},
      {
        $group: {
          _id: '$uid',
          amount: {$sum: '$value'}, // 对 amount 字段求和
        },
      },
    ])
    let categoryList = [
      'refund',
      'order',
      'self_study',
      'service',
      'points_purchase',
      'invite',
      'session',
      'service_substitute',
      'saas_tool_trail',
      'saas_tool_paid',
      'service_correct',
      'service_premium',
      'verify',
      'unit',
      'task',
    ]
    let countArr: any = {}
    for (let i = 0; i < categoryList.length; i++) {
      const item = categoryList[i]
      let count = await this.Model.count({uid, category: item})
      countArr[item] = count
    }
    let expectedCount = await this.Model.count({uid, status: 0})
    let ActualCount = await this.Model.count({uid, status: 1})

    return {
      expected_amount: expectedAmount.length > 0 ? expectedAmount[0].amount : 0,
      ...countArr,
      expected_count: expectedCount,
      actual_count: ActualCount,
    }
    // await this.goodsFilter({buyer: params?.user?._id}, params.user)
    // let count = await this.Model.count({buyer: params.user?._id})
    // return {
    //   count,
    // }
  }
  /**
   * 增加log,同时更新用户积分
   * amount 积分 或者金额美分
   */
  async getAddLog(
    {uid, inviter, change, tab, source, category, categoryType, amount = 0, businessId = '', snapshot, isSchool = false, status = 1}: any,
    params?: TxnParams
  ): Promise<any> {
    const options = Acan.getTxnOptions(params)
    let user: any
    if (uid) {
      if (isSchool) {
        user = await this.app.service('school-plan').Model.findOne({_id: uid}, null, options)
      } else {
        user = await this.app.service('users').Model.findOne({_id: uid}, null, options)
      }
    } else {
      user = await this.app.service('users').Model.findOne({inviteCode: inviter}, null, options)
    }

    let value = change
    if (!change) {
      let resPoint = await this.app.service('point-setting').calcPoint({type: {category}, amount, tab, isPoint: false})
      value = resPoint.point
    }

    let total = user.point
    if (status == 1) {
      total = Number((user.point + Number(value)).toFixed(0))
      if (isSchool) {
        await this.app.service('school-plan').Model.findByIdAndUpdate(user._id, {point: total}, options)
      } else {
        await this.app.service('users').Model.findByIdAndUpdate(user._id, {point: total}, options)
      }
    }
    let logData: any = await this.Model.findOne(
      {
        uid: user._id.toString(),
        tab,
        source,
        category,
        categoryType,
        businessId: businessId.toString(),
        isSchool,
      },
      null,
      options
    )
    return this.Model.findOneAndUpdate(
      {
        uid: user._id.toString(),
        tab,
        source,
        category,
        categoryType,
        businessId: businessId.toString(),
        isSchool,
      },
      {
        $set: {
          uid: user._id.toString(),
          tab,
          source,
          category,
          categoryType,
          value,
          total,
          businessId: businessId.toString(),
          snapshot,
          isSchool,
          status,
          expected: logData?.value,
        },
      },
      {upsert: true, ...options}
    )
  }

  /**
   * 弃用 改为service('point-setting').calcPoint
   * 计算应获得的积分值
   * (0,1] => 1
   * 1以上向下取整
   * amount 积分 或者金额美分
   */
  // async getPointValue({tab, category, categoryType, amount}: any): Promise<any> {
  //   let query: any = {
  //     tab,
  //     category,
  //   }
  //   if (categoryType) query.categoryType = categoryType
  //   let setting: any = await this.app.service('point-setting').Model.findOne(query)
  //   if (!setting) return 0

  //   if (setting.mode === 'fixed') {
  //     return setting.value
  //   } else {
  //     let num = (setting.value / 100) * amount
  //     if (!num) return 0
  //     if (num <= 1) return 1
  //     return Math.floor(num)
  //   }
  // }

  // 增加积分佣金log添加
  async handleAddLog(log: any): Promise<any> {
    if (log.type == 'point') {
      await this.getAddLog(log)
    } else if (log.type == 'commission') {
      await this.app.service('commission-log').getAddLog(log)
    }
  }
}
