"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Reviews = void 0;
const feathers_mongoose_1 = require("feathers-mongoose");
class Reviews extends feathers_mongoose_1.Service {
    constructor(options, app) {
        super(options);
        this.app = app;
    }
    async getMy(query, params) {
        var _a;
        return this.Model.findOne({ ...query, uid: (_a = params.user) === null || _a === void 0 ? void 0 : _a._id });
    }
    async getStat({ rid }, params) {
        const con = { rid };
        const arr = await this.app.get('redisHCache')('reviewStat:rates', rid, async () => {
            return await this.Model.aggregate([
                { $match: con },
                { $group: { _id: "$rate", count: { $sum: 1 } } }
            ]);
        });
        // const grades = await this.app.get('redisHCache')('reviewStat:grades', rid, async () => {
        const list = await this.Model.find(con).select(['grade']);
        const grades = {};
        list.map(({ grade }) => {
            if (!grade)
                return;
            for (let i = grade[0]; i <= grade[1]; i++) {
                if (!grades[i])
                    grades[i] = 0;
                grades[i] += 1;
            }
        });
        // return grades
        // })
        const rates = {};
        let num = 0, total = 0;
        for (let i = 1; i <= 5; i++)
            rates[i] = 0;
        arr.map((v) => {
            rates[v._id] = v.count;
            total += v._id * v.count;
            num += v.count;
        });
        return { rate: parseFloat((total / num).toFixed(1)), rates, grades };
    }
}
exports.Reviews = Reviews;
