"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const share_info_class_1 = require("./share-info.class");
const share_info_model_1 = __importDefault(require("../../models/share-info.model"));
const share_info_hooks_1 = __importDefault(require("./share-info.hooks"));
function default_1(app) {
    const options = {
        Model: (0, share_info_model_1.default)(app),
        paginate: app.get('paginate'),
    };
    // Initialize our service with any options it requires
    app.use('/share-info', new share_info_class_1.ShareInfo(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('share-info');
    service.hooks(share_info_hooks_1.default);
}
exports.default = default_1;
