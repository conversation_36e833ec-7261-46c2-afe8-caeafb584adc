import {HookContext} from '@feathersjs/feathers'
import * as authentication from '@feathersjs/authentication'
// Don't remove this comment. It's needed to format import lines nicely.

const {authenticate} = authentication.hooks
import mod from './task-outline.mod'
import logger from '../../logger'
import {ObjectID} from 'bson'
import hook from '../../hook'

export default {
  before: {
    all: [],
    find: [
      authenticate('jwt'),
      (d: HookContext) => {
        if (d.params.query?.$dev) {
          d.params.dev = 1
          delete d.params.query.$dev
        }
      },
    ],
    get: [
      hook.toClass,
      async (d: any) => {
        logger.warn(d.id, 'task-outline')
        if (hook.classExist(d)) return d
        if (d.id && mod[d.id]) return mod[d.id](d)
        else if (/^\d+$/.test(d.id + '')) {
          d.result = await d.service.Model.findOne({task: d.id})
        } else return d
      },
    ],
    create: [/** @transactional | `params` 可能包含一个 Mongoose session，所有数据库调用以及新添加的嵌套函数必须使用该 session。*/ authenticate('jwt')],
    update: [authenticate('jwt')],
    patch: [
      authenticate('jwt'),
      (d: HookContext) => {
        for (const key of Object.keys(d.data)) {
          // auto parse unique subjects
          const krr = key.split('.')
          const lastKey = krr.pop()
          if (lastKey === 'data') {
            const srr: any = []
            Object.keys(d.data[key]).map((code) => {
              const subject = code.split(':')[1]
              if (srr.includes(subject)) return
              srr.push(subject)
            })
            d.data[key.replace('.data', '.subjects')] = srr
          } else if (lastKey === 'custom') {
            d.data[key].map((v: any) => {
              if (!v._id) v._id = new ObjectID().toString()
            })
          }
        }
      },
    ],
    remove: [authenticate('jwt')],
  },

  after: {
    all: [],
    find: [],
    get: [],
    create: [
      /** @transactional | `params` 可能包含一个 Mongoose session，所有数据库调用以及新添加的嵌套函数必须使用该 session。*/
      async (d: any) => {
        // pd add subject to outline
        const {task} = d.result
        const $set = {}
        await d.service.unitServiceSet(task, d.data, $set, d.params)
      },
    ],
    update: [],
    patch: [
      async (d: any) => {
        // related patch to unit.outlineSubjects
        let needUp = false
        for (const key of Object.keys(d.data)) {
          if (key.split('.').pop() === 'data') needUp = true
        }
        if (!needUp) return
        const {task} = d.result
        const $set: any = {}
        const outlineSubjects: any = []
        for (const type of ['assess', 'outline', 'pd']) {
          if (!d.result[type]?.data) continue
          for (const code in d.result[type].data) {
            const {name} = d.result[type].data[code] ?? {}
            if (!name) continue
            if (outlineSubjects.includes(name)) continue
            outlineSubjects.push(name)
          }
        }
        if (!Acan.isEmpty(outlineSubjects)) $set.outlineSubjects = outlineSubjects
        // pd add subject to outline
        await d.service.unitServiceSet(task, d.data, $set)
      },
    ],
    remove: [],
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: [],
  },
}
