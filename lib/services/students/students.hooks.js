"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const authentication = __importStar(require("@feathersjs/authentication"));
const { authenticate } = authentication.hooks;
const { NotFound, GeneralError, BadRequest } = require('@feathersjs/errors');
const hook_1 = __importDefault(require("../../hook"));
// Don't remove this comment. It's needed to format import lines nicely.
exports.default = {
    before: {
        all: [],
        find: [authenticate('jwt')],
        get: [
            async (d) => {
                // 排序无需认证的接口
                if (!['parentJoin'].includes(d.id + ''))
                    await authenticate('jwt')(d);
                return await hook_1.default.toClass(d);
            },
        ],
        create: [
            authenticate('jwt'),
            async (d) => {
                var _a;
                let num = 0, school;
                if (!Array.isArray(d.data))
                    d.data = [d.data];
                for (const o of d.data) {
                    o.school = o.school || ((_a = d.params.user) === null || _a === void 0 ? void 0 : _a._id);
                }
                school = d.data[0].school;
                num = d.data.length;
                if (!num)
                    return await Promise.reject(new Error('Error data'));
                await d.app.service('users').checkPlanLimit({ school, num, type: 'student' }, d.params);
                // const list = await d.service.Model.find({school}).select(['email', 'name', 'parent.email'])
                // const emailList = {}
                // const parentList = {}
                // for (const o of list) {
                //   if (o.email) emailList[o.email] = 1
                //   if (o.parent.email) parentList[[o.parent.email, ...name].join('-')] = 1
                // }
                for (const o of d.data) {
                    if (o.id) {
                        // 使用 id 增加
                    }
                    else if (o.email) {
                        // 使用 邮箱 增加学生
                    }
                    else {
                        // 创建 id
                        o.id = await d.service.randId();
                        await d.service.autoCreateUser(o, d.params);
                    }
                    // todo check parent.email + name unique
                    // if (o.parent.email) {
                    // // todo check email unique
                    // } else (o.email) {
                    //   const await d.service.Model.count({school, email})
                    // }
                }
                setTimeout(() => {
                    d.app.get('redis').HDEL('StatSchool', school).then();
                }, 100);
            },
        ],
        update: [authenticate('jwt'), hook_1.default.disable],
        patch: [
            authenticate('jwt'),
            async (d) => {
                // 导入学科班的学生 清理报名记录
                if (d.data.$import) {
                    delete d.data.$import;
                    await d.app.service('class-apply').clearApply({ classId: d.data.$addToSet.subjectClass, student: d.id });
                }
                await d.service.mailModify(d.data, d.params, d.id);
                // 还原学生的时候需要检查套餐
                if (d.data.del === false) {
                    const { school } = (await d.service.Model.findOne({ _id: d.id }).select('school')) || {};
                    if (school) {
                        await d.app.service('users').checkPlanLimit({ school, num: 1, type: 'student' }, d.params);
                    }
                    d.app.get('redis').HDEL('StatSchool', school).then();
                }
            },
        ],
        remove: [authenticate('jwt')],
    },
    after: {
        all: [],
        find: [
            async (d) => {
                if (d.result) {
                    for (let i = 0; i < d.result.data.length; i++) {
                        await d.service.extClass(d.result.data[i]);
                    }
                }
            },
        ],
        get: [],
        create: [
            async (d) => {
                var _a;
                const arr = ((_a = d.result) === null || _a === void 0 ? void 0 : _a._id) ? [d.result] : Array.isArray(d.result) ? d.result : null;
                if (!arr)
                    return (d.result = { message: 'create error' }), d;
                await d.service.count(arr[0], d.params);
                const { class: classes, status } = d.data[0] || d.data;
                if (classes) {
                    for (const classId of classes)
                        await d.service.countClass({ class: classId }, d.params);
                }
                if (!status) {
                    let idList = d.data.map((o) => o.id);
                    d.service.sendAll(arr, idList, d.params);
                }
            },
        ],
        update: [],
        patch: [
            async (d) => {
                const { $addToSet, $pull } = d.data;
                const { email, school } = d.result;
                d.app.get('redis').HDEL('students:Info' + school, email);
                if (Acan.isDefined(d.data.del)) {
                    await d.service.count(d.result, d.params);
                    await d.service.countClass({ class: d.result.class }, d.params);
                    await d.service.countSubjectClass({ subjectClass: d.result.subjectClass }, d.params);
                    await d.service.clearSubjectClass({ doc: d.result });
                }
                if (d.data.del === true) {
                    d.service.sendNotice(d.result, 'StudentRemovedFromStandardClass', d.params);
                }
                if (d.data.del === false) {
                    d.service.sendNotice(d.result, 'StudentAddedToStandardClass', d.params);
                }
                if ($pull === null || $pull === void 0 ? void 0 : $pull.class)
                    await d.service.countClass({ class: $pull.class }, d.params);
                if ($addToSet === null || $addToSet === void 0 ? void 0 : $addToSet.class)
                    await d.service.countClass({ class: $addToSet.class }, d.params);
                if ($pull === null || $pull === void 0 ? void 0 : $pull.subjectClass) {
                    await d.service.countSubjectClass({ subjectClass: $pull.subjectClass }, d.params);
                    await d.app.service('class-apply').clearApply({ classId: $pull.subjectClass, student: d.id });
                }
                if ($addToSet === null || $addToSet === void 0 ? void 0 : $addToSet.subjectClass) {
                    await d.service.countSubjectClass({ subjectClass: $addToSet.subjectClass }, d.params);
                    await d.app.service('class-apply').checkLimit({ classId: $addToSet.subjectClass });
                }
            },
        ],
        remove: [
            async (d) => {
                if (!d.result)
                    return;
                await d.service.count(d.result, d.params);
                await d.service.countClass({ class: d.result.class }, d.params);
                await d.service.countSubjectClass({ subjectClass: d.result.subjectClass }, d.params);
                const { school, email, nickname, uid } = Array.isArray(d.result) ? d.result[0] : d.result;
                if (school)
                    await d.app.get('redis').HDEL('StatSchool', school);
            },
        ],
    },
    error: {
        all: [],
        find: [],
        get: [],
        create: [],
        update: [],
        patch: [],
        remove: [],
    },
};
