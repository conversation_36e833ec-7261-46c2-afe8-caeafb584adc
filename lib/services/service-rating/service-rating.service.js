"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const service_rating_class_1 = require("./service-rating.class");
const service_rating_model_1 = __importDefault(require("../../models/service-rating.model"));
const service_rating_hooks_1 = __importDefault(require("./service-rating.hooks"));
function default_1(app) {
    const options = {
        Model: (0, service_rating_model_1.default)(app),
        paginate: app.get('paginate')
    };
    // Initialize our service with any options it requires
    app.use('/service-rating', new service_rating_class_1.ServiceRating(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('service-rating');
    service.hooks(service_rating_hooks_1.default);
}
exports.default = default_1;
