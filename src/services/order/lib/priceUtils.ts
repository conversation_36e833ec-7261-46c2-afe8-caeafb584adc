interface Order {
  links: Array<{id: string; price: number}>
  price: number
  cash?: number
  giftCard?: number
}
export function allocatePayments(order: Order) {
  const totalPrice = order.price
  const giftCardAmount = order.giftCard || 0

  let totalGiftCardAllocated = 0

  const updatedLinks = order.links.map((link, index) => {
    const proportion = link.price / totalPrice
    let giftCardForItem = Math.round(giftCardAmount * proportion)

    if (index === order.links.length - 1) {
      giftCardForItem = giftCardAmount - totalGiftCardAllocated
    } else {
      totalGiftCardAllocated += giftCardForItem
    }

    const cashForItem = link.price - giftCardForItem

    return {
      ...link,
      giftCard: giftCardForItem,
      cash: cashForItem,
    }
  })

  return {
    ...order,
    links: updatedLinks,
  }
}

export function calculateItemRefund(
  item: {
    id: string
    price: number
    giftCard?: number
    cash?: number
    refundCash?: number
    refundGiftCard?: number
  },
  refundAmount: number
) {
  const cashRefunded = item.refundCash || 0
  const giftCardRefunded = item.refundGiftCard || 0
  const alreadyRefunded = cashRefunded + giftCardRefunded
  const remainingRefundable = item.price - alreadyRefunded
  const actualRefundAmount = Math.min(refundAmount, remainingRefundable)

  const remainingGiftCard = (item.giftCard || 0) - giftCardRefunded

  const giftCardRefund = Math.min(actualRefundAmount, remainingGiftCard)
  const cashRefund = actualRefundAmount - giftCardRefund

  return {
    giftCardRefund,
    cashRefund,
  }
}
