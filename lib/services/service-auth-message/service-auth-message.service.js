"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const service_auth_message_class_1 = require("./service-auth-message.class");
const service_auth_message_model_1 = __importDefault(require("../../models/service-auth-message.model"));
const service_auth_message_hooks_1 = __importDefault(require("./service-auth-message.hooks"));
function default_1(app) {
    const options = {
        Model: (0, service_auth_message_model_1.default)(app),
        paginate: app.get('paginate')
    };
    // Initialize our service with any options it requires
    app.use('/service-auth-message', new service_auth_message_class_1.ServiceAuthMessage(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('service-auth-message');
    service.hooks(service_auth_message_hooks_1.default);
}
exports.default = default_1;
