"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const paypal_class_1 = require("./paypal.class");
const paypal_hooks_1 = __importDefault(require("./paypal.hooks"));
function default_1(app) {
    const options = {
        paginate: app.get('paginate')
    };
    // Initialize our service with any options it requires
    app.use('/paypal', new paypal_class_1.Paypal(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('paypal');
    service.hooks(paypal_hooks_1.default);
}
exports.default = default_1;
