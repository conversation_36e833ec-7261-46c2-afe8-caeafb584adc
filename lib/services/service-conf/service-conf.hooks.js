"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const authentication = __importStar(require("@feathersjs/authentication"));
const hook_1 = __importDefault(require("../../hook"));
const errors_1 = require("@feathersjs/errors");
// Don't remove this comment. It's needed to format import lines nicely.
const { authenticate } = authentication.hooks;
exports.default = {
    before: {
        all: [authenticate('jwt')],
        find: [hook_1.default.sysQuery('uid', true, ['customer_service', 'customer_service_manager', 'accountant', 'admin'])],
        get: [hook_1.default.toClass],
        create: [],
        update: [],
        patch: [
            (d) => {
                const { hours, attachmentsVetting, vettingDate } = d.data;
                if (hours) {
                    let max = 0;
                    const hoursIndex = hours.map((arr) => {
                        const min = Math.floor((new Date(arr[1]).getTime() - new Date(arr[0]).getTime()) / 60000);
                        if (max < min)
                            max = min;
                        return [getUTCHour(arr[0]), getUTCHour(arr[1])];
                    });
                    d.data.hoursIndex = hoursIndex;
                    d.data.hoursMax = max;
                    if (attachmentsVetting) {
                        d.data.vettingReminder = false;
                        d.data.vettingExpiredReminder = false;
                    }
                }
                if (vettingDate) {
                    d.data.vettingReminder = false;
                    d.data.vettingExpiredReminder = false;
                }
            },
        ],
        remove: [],
    },
    after: {
        all: [],
        find: [
            async (d) => {
                var _a;
                for (const one of ((_a = d.result) === null || _a === void 0 ? void 0 : _a.data) || []) {
                    await d.service.ext(one);
                }
                return d;
            },
        ],
        get: [
            async (d) => {
                var _a;
                await d.service.ext(d.result);
                const { _id } = d.result;
                const { booking, session } = (_a = d.params.query) !== null && _a !== void 0 ? _a : {};
                if (booking)
                    d.result.booking = await d.app.service('service-booking').getHours({ uid: _id });
                if (session)
                    d.result.session = await d.app.service('session').getHours({ uid: _id });
            },
        ],
        create: [
            async (d) => {
                let { place_id } = d.data;
                if (place_id) {
                    let { _id } = d.result;
                    d.app.service('campus-location').updateLocation({ _id, place_id, model: 'service-conf' });
                }
            },
        ],
        update: [],
        patch: [
            async (d) => {
                var _a;
                const uid = (_a = d.params.user) === null || _a === void 0 ? void 0 : _a._id;
                if (!uid)
                    return Promise.reject(new errors_1.NotAuthenticated());
                for (const key in d.data) {
                    if (!key.includes('enable.'))
                        continue;
                    const [type, mentoringType] = key.substring(7).split(':');
                    const con = { uid, type, mentoringType };
                    Acan.objClean(con);
                    // 批量更新认证的开启状态
                    await d.app.service('service-auth').Model.updateMany(con, { enable: d.data[key] });
                }
                let { place_id } = d.data;
                if (place_id) {
                    let { _id } = d.result;
                    d.app.service('campus-location').updateLocation({ _id, place_id, model: 'service-conf' });
                }
            },
        ],
        remove: [],
    },
    error: {
        all: [],
        find: [],
        get: [],
        create: [],
        update: [],
        patch: [],
        remove: [],
    },
};
