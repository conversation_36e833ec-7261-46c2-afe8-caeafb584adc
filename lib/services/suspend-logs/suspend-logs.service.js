"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const suspend_logs_class_1 = require("./suspend-logs.class");
const suspend_logs_model_1 = __importDefault(require("../../models/suspend-logs.model"));
const suspend_logs_hooks_1 = __importDefault(require("./suspend-logs.hooks"));
function default_1(app) {
    const options = {
        Model: (0, suspend_logs_model_1.default)(app),
        paginate: app.get('paginate')
    };
    // Initialize our service with any options it requires
    app.use('/suspend-logs', new suspend_logs_class_1.SuspendLogs(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('suspend-logs');
    service.hooks(suspend_logs_hooks_1.default);
}
exports.default = default_1;
