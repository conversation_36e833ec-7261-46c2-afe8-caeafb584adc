"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServiceRating = void 0;
const feathers_mongoose_1 = require("feathers-mongoose");
class ServiceRating extends feathers_mongoose_1.Service {
    //eslint-disable-next-line @typescript-eslint/no-unused-vars
    constructor(options, app) {
        super(options);
        this.app = app;
    }
    async extBooker(one) {
        one.bookerInfo = await this.app.service('users').uidToInfo(one.booker);
    }
    // 按标签统计数量
    getTagsCount({ servicer }, params) {
        return this.Model.aggregate([
            { $match: { servicer } },
            { $project: { _id: 0, tags: 1 } },
            { $unwind: '$tags' },
            { $group: { _id: '$tags', count: { $sum: 1 } } },
            { $project: { _id: 0, tags: '$_id', count: 1 } },
            { $sort: { count: -1 } },
        ]);
    }
}
exports.ServiceRating = ServiceRating;
