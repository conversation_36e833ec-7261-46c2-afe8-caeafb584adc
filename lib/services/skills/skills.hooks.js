"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const authentication = __importStar(require("@feathersjs/authentication"));
const hook_1 = __importDefault(require("../../hook"));
// Don't remove this comment. It's needed to format import lines nicely.
const { authenticate } = authentication.hooks;
exports.default = {
    before: {
        all: [authenticate('jwt')],
        find: [
            async (d) => {
                var _a, _b, _c;
                const query = (_a = d.params.query) !== null && _a !== void 0 ? _a : {};
                const uid = (query === null || query === void 0 ? void 0 : query.uid) || ((_b = d.params.user) === null || _b === void 0 ? void 0 : _b._id);
                if (!hook_1.default.roleHas(['sys'])(d))
                    query.uid = uid;
                query.$select = (_c = query.$select) !== null && _c !== void 0 ? _c : d.service.selectList;
                if (query.isLib) {
                    query.$select = hook_1.default.selectLib(query.$select, d.service.publishKeys);
                    d.params.isLib = true;
                    query.del = false;
                }
                delete query.isLib;
            },
        ],
        get: [
            hook_1.default.toClass,
            (d) => {
                var _a;
                const query = (_a = d.params.query) !== null && _a !== void 0 ? _a : {};
                if (query.isLib)
                    d.params.isLib = true;
                delete query.isLib;
            },
        ],
        create: [],
        update: [],
        patch: [
            async (d) => {
                var _a;
                const { snapshot } = (_a = d.data) !== null && _a !== void 0 ? _a : {};
                if (snapshot) {
                    await d.service.setSnapshot(d.id, d.data);
                }
            },
        ],
        remove: [],
    },
    after: {
        all: [],
        find: [
            (d) => {
                if (d.params.isLib)
                    d.result.data = hook_1.default.resultLib(d.result.data, d.service.publishKeys);
            },
        ],
        get: [
            (d) => {
                if (d.params.isLib) {
                    hook_1.default.resultLibOne(d.result, d.service.publishKeys);
                    return d;
                }
            },
        ],
        create: [
            (d) => {
                var _a;
                d.data.uid = d.data.uid || ((_a = d.params.user) === null || _a === void 0 ? void 0 : _a._id);
                d.app.service('curric').count({ school: d.data.uid, model: 'skills' });
            },
        ],
        update: [],
        patch: [
            (d) => {
                if (d.data.curriculum) {
                    const { uid, _id } = Acan.clone(d.result);
                    d.app.service('curric').count({ school: uid, model: 'skills' });
                }
            },
        ],
        remove: [
            (d) => {
                const { uid, _id } = Acan.clone(d.result);
                d.app.service('curric').count({ school: uid, model: 'skills' });
            },
        ],
    },
    error: {
        all: [],
        find: [],
        get: [],
        create: [],
        update: [],
        patch: [],
        remove: [],
    },
};
