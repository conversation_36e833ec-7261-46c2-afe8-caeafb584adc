"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const order_class_1 = require("./order.class");
const order_model_1 = __importDefault(require("../../models/order.model"));
const order_hooks_1 = __importDefault(require("./order.hooks"));
function default_1(app) {
    const options = {
        Model: (0, order_model_1.default)(app),
        whitelist: ['$regex', '$options', '$search', '$elemMatch', '$exists'],
        paginate: app.get('paginate'),
    };
    // Initialize our service with any options it requires
    app.use('/order', new order_class_1.Order(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('order');
    service.hooks(order_hooks_1.default);
}
exports.default = default_1;
