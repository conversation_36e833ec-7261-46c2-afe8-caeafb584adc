"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServicePackSchoolPrice = void 0;
const feathers_mongoose_1 = require("feathers-mongoose");
class ServicePackSchoolPrice extends feathers_mongoose_1.Service {
    //eslint-disable-next-line @typescript-eslint/no-unused-vars
    constructor(options, app) {
        super(options);
        this.app = app;
    }
    // 数量统计
    async getCount({ school }, params) {
        let count = await this.Model.count({ school });
        return {
            count,
        };
    }
    async extServicePack(one, params) {
        one.servicePackInfo = await this.app.service('service-pack').Model.findOne({ _id: one.servicePack });
    }
    // async getSave(data: any, params: Params): Promise<any> {
    //   let {school, servicePack, students} = data
    //   await this.Model.findOneAndUpdate(
    //     {
    //       school,
    //       servicePack,
    //     },
    //     {
    //       $set: data,
    //     },
    //     {upsert: true}
    //   )
    // }
    async shareEmailStudent({ servicePack, deadline, students, school }) {
        let studentList = await this.app.service('students').Model.find({
            uid: { $in: students },
            school,
            $select: ['email', 'name'],
        });
        let servicePackData = await this.app.service('service-pack').Model.findOne({ _id: servicePack });
        for (let i = 0; i < studentList.length; i++) {
            const item = studentList[i];
            let url = `${SiteUrl}/v2/service/pack/${servicePack}?sharedSchool=${school}`;
            let date = new Date(deadline).toLocaleString();
            if (!item.email || item.email.indexOf('@classcipe.com') > -1) {
                this.app.service('notice-tpl').mailto('ReminderToApplyPremiumContentCourse(parent)', item.parent.email, {
                    username: item.name.join(' '),
                    name: servicePackData.name,
                    date,
                    url,
                });
            }
            else {
                this.app
                    .service('notice-tpl')
                    .mailto('ReminderToApplyPremiumContentCourse(student)', item.email, { username: item.name.join(' '), name: servicePackData.name, date, url });
            }
        }
    }
    async shareEmailTeacher({ servicePack, deadline, teachers, school }) {
        let studentList = await this.app.service('school-user').Model.find({
            uid: { $in: teachers },
            school,
            $select: ['email', 'name'],
        });
        let servicePackData = await this.app.service('service-pack').Model.findOne({ _id: servicePack });
        for (let i = 0; i < studentList.length; i++) {
            const item = studentList[i];
            let url = `${SiteUrl}/v2/service/pack/${servicePack}?sharedSchool=${school}`;
            let date = new Date(deadline).toLocaleString();
            this.app
                .service('notice-tpl')
                .mailto('ReminderToApplyPremiumContentCourse(student)', item.email, { username: item.name.join(' '), name: servicePackData.name, date, url });
        }
    }
    // 设置每个用户各自的价格
    async setApplyPrice({ contentOrientated, servicePack, school }) {
        let a = await this.app
            .service('service-pack-apply')
            .Model.updateMany({ servicePack: servicePack, sharedSchool: school }, { contentOrientated, purchaseExpireAt: Date.now() + 7 * 24 * 3600 * 1000 });
    }
    async cron1({}, params) {
        this.timeoutRemove();
    }
    // 超时未通过删除
    timeoutRemove() {
        this.Model.find({ deadline: { $lt: Date.now() - 37 * 24 * 3600 * 1000, $gt: Date.now() - 38 * 24 * 3600 * 1000 } }).then(async (rs) => {
            for (let i = 0; i < rs.length; i++) {
                const item = rs[i];
                await this.app.service('service-pack-apply').Model.deleteMany({ servicePack: item.servicePack, sharedSchool: item.school, status: 0 });
            }
        });
    }
}
exports.ServicePackSchoolPrice = ServicePackSchoolPrice;
