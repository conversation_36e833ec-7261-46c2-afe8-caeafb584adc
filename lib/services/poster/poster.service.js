"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const poster_class_1 = require("./poster.class");
const poster_model_1 = __importDefault(require("../../models/poster.model"));
const poster_hooks_1 = __importDefault(require("./poster.hooks"));
function default_1(app) {
    const options = {
        Model: (0, poster_model_1.default)(app),
        paginate: app.get('paginate'),
    };
    // Initialize our service with any options it requires
    app.use('/poster', new poster_class_1.Poster(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('poster');
    service.hooks(poster_hooks_1.default);
}
exports.default = default_1;
