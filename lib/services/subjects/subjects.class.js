"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Subjects = void 0;
const feathers_mongoose_1 = require("feathers-mongoose");
const bson_1 = require("bson");
class Subjects extends feathers_mongoose_1.Service {
    //eslint-disable-next-line @typescript-eslint/no-unused-vars
    constructor(options, app) {
        super(options);
        this.app = app;
        this.publishKeys = ['standard', 'topic', 'standardLevel', 'topicLevel'];
        this.selectList = [
            'uid',
            'del',
            'name',
            'subtitle',
            'curriculum',
            'participants',
            'grade',
            'standardLevel',
            'topicLevel',
            'count',
            'code',
            'source',
            'publish',
            'subjectCode',
            'coordinator',
            'updatedAt',
            'createdAt',
        ];
    }
    async getSubId() {
        return new bson_1.ObjectID().toString();
    }
    async setSnapshot(_id, data) {
        var _a;
        const doc = Acan.clone(await this.Model.findById(_id));
        const snapshot = {};
        if (doc.snapshot) {
            for (const key of ['_id', ...this.publishKeys]) {
                if (doc.snapshot[key])
                    snapshot[key] = doc.snapshot[key];
            }
        }
        const publish = (_a = doc.publish) !== null && _a !== void 0 ? _a : [];
        function setKey(key) {
            if (!Acan.isEmpty(doc[key])) {
                if (!publish.includes(key))
                    publish.push(key);
            }
            else {
                const i = publish.indexOf(key);
                if (i !== -1)
                    publish.splice(i, 1);
            }
            snapshot[key] = doc[key] || [];
            snapshot[`${key}Level`] = doc[`${key}Level`] || [];
        }
        if (data.snapshot === true) {
            setKey('standard');
            setKey('topic');
        }
        else {
            setKey(data.snapshot);
        }
        data.publish = publish;
        data.snapshot = snapshot;
    }
    async idToParticipants($in) {
        const query = {};
        if (Acan.isObjectId($in[0])) {
            query._id = { $in };
        }
        else {
            return this.app.get('pdSubjectCodeMap')[$in[0]];
        }
        const rs = await this.Model.findOne(query).select('participants');
        if (!rs)
            return;
        return rs.participants;
    }
    async getGradesCount({ school }, params) {
        var _a;
        const uid = school || ((_a = params.user) === null || _a === void 0 ? void 0 : _a._id);
        if (!uid)
            return {};
        const arr = await this.Model.find({ uid }).select(['grade']);
        const rs = {};
        for (const o of arr) {
            if (o.grade)
                for (const grade of o.grade) {
                    if (!rs[grade])
                        rs[grade] = 1;
                    else
                        rs[grade]++;
                }
        }
        return rs;
    }
    /**
     * 判断Topic数据是否被删,只判断每层_id字段,不判断增加
     */
    matchTopic(oldData, newData, targetLevel = 1, currentLevel = 1, labels = []) {
        let oldMap = {};
        let newMap = {};
        for (let i = 0; i < oldData.length; i++) {
            const oldItem = oldData[i];
            if (oldItem && oldItem._id) {
                oldMap[oldItem._id] = oldItem;
            }
        }
        for (let i = 0; i < newData.length; i++) {
            const newItem = newData[i];
            newMap[newItem._id] = newItem;
        }
        for (let key in oldMap) {
            if (oldMap[key] && !newMap[key]) {
                labels.push(oldMap[key].name);
            }
            else {
                if (targetLevel > currentLevel && oldMap[key].child && oldMap[key].child.length > 0) {
                    let res = this.matchTopic(oldMap[key].child, newMap[key].child, targetLevel, currentLevel + 1, labels);
                    labels = res.labels;
                }
            }
        }
        return {
            labels,
        };
    }
    async sendAll(doc, tpl, labels, params) {
        const { _id } = doc;
        let query = { status: [1, 2] };
        if (labels.length > 0) {
            query['topic.label'] = { $in: labels };
        }
        else {
            query['subject'] = _id;
        }
        let authData = await this.app.service('service-auth').Model.find(query);
        let url = '';
        if (tpl === 'VerificationStatusChanged')
            url = `${SiteUrl}/v2/account/teacher/auth/introduction`;
        for (let i = 0; i < authData.length; i++) {
            const authItem = authData[i];
            let name = await this.app.service('service-auth').getAuthName(authItem);
            this.send({ uid: authItem.uid, name: name.join('-'), url }, tpl, params);
        }
        // 删除已经申请的认证项, #4754
        // 已经认证通过的不删除，#4860
        query.status = { $ne: 2 };
        this.app.service('service-auth').Model.deleteMany(query).then();
    }
    async send(doc, tpl, params) {
        var _a;
        const { uid, name, url } = doc;
        const user = await this.app.service('users').uidToInfo(uid);
        return await this.app.service('notice-tpl').mailto(tpl, user.email, { username: user.name.join(' '), name: name, url: url }, (_a = params.user) === null || _a === void 0 ? void 0 : _a._id);
    }
    async getCoordinator({ school }, params) {
        let subjectsData = await this.Model.find({ uid: school, coordinator: { $exists: true } })
            .sort({ updatedAt: -1 })
            .lean();
        let list = [];
        for (let i = 0; i < subjectsData.length; i++) {
            const item = subjectsData[i];
            for (let j = item.coordinator.length - 1; j >= 0; j--) {
                const coordinator = item.coordinator[j];
                let user = await this.app.service('users').uidToInfo(coordinator);
                list.push({
                    ...item,
                    userInfo: user,
                });
            }
        }
        return list;
    }
}
exports.Subjects = Subjects;
