"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const skills_class_1 = require("./skills.class");
const skills_model_1 = __importDefault(require("../../models/skills.model"));
const skills_hooks_1 = __importDefault(require("./skills.hooks"));
function default_1(app) {
    const options = {
        Model: (0, skills_model_1.default)(app),
        whitelist: ['$exists', '$regex', '$options', '$search'],
        paginate: app.get('paginate'),
    };
    // Initialize our service with any options it requires
    app.use('/skills', new skills_class_1.Skills(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('skills');
    service.hooks(skills_hooks_1.default);
}
exports.default = default_1;
