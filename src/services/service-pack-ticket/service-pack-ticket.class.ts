import {Service, MongooseServiceOptions} from 'feathers-mongoose'
import {Application} from '../../declarations'
import {Params} from '@feathersjs/feathers'
import {TxnParams} from '../../dbTransactions'
const {NotFound, GeneralError, BadRequest} = require('@feathersjs/errors')

export class ServicePackTicket extends Service {
  app: Application
  //eslint-disable-next-line @typescript-eslint/no-unused-vars
  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.app = app
  }

  async extUser(one: any, params?: Params) {
    if (one.uid) {
      one.userInfo = await this.app.service('users').uidToInfo(one.uid)
    }
  }
  async extServicePack(data: any, params?: Params) {
    let dict: any = {}
    for (let i = 0; i < data.length; i++) {
      const item = data[i]
      for (let j = 0; j < item.serviceData.length; j++) {
        const service = item.serviceData[j]
        if (!dict[service.servicePack]) {
          dict[service.servicePack] = await this.app.service('service-pack').Model.findOne({_id: service.servicePack})
        }
        service.serviceInfo = dict[service.servicePack]
      }
    }
  }
  // 生成
  async generate({persons, school, servicePremium, order, links}: any, params?: TxnParams): Promise<any> {
    const options = Acan.getTxnOptions(params)
    let ticket: any = {
      school,
      servicePremium,
      order,
    }
    let serviceData = []
    for (let i = 0; i < links.length; i++) {
      let {price, point, count, giftCount, style} = links[i]
      if (style === 'service') {
        let cashCount = 0
        let pointCount = 0
        if (price > 0) {
          cashCount = count
        } else if (point > 0) {
          pointCount = count
        }
        serviceData.push({
          servicePack: links[i].id,
          cash: cashCount,
          point: pointCount,
          gift: giftCount,
          cashOrigin: cashCount,
          pointOrigin: pointCount,
          giftOrigin: giftCount,
        })
      }
    }
    ticket.serviceData = serviceData

    let insetData = []
    for (let i = 0; i < persons; i++) {
      // let code = Acan.MD5(order + i).substr(0, 6)
      // ticket.code = code
      insetData.push(Acan.clone(ticket))
    }
    // 同步service-pack.count.ticket
    for (let i = 0; i < serviceData.length; i++) {
      await this.app.service('service-pack').Model.updateOne({_id: serviceData[i].servicePack}, {$inc: {'count.ticket': persons}}, options)
    }
    return await this.Model.create(insetData, options)
  }

  // 分配用户
  async getClaim({id, uid}: any, params: Params): Promise<any> {
    let ticket: any = await this.Model.findOne({_id: id}).lean()
    if (ticket.uid) {
      return Promise.reject(new GeneralError('Claimed'))
    }
    if (ticket.refund) {
      return Promise.reject(new GeneralError('Refunded'))
    }
    let user = Acan.clone(await this.app.service('users').uidToInfo(uid))

    let serviceApplyData: any = await this.app
      .service('service-pack-apply')
      .Model.findOne({uid, servicePack: ticket.servicePremium, sharedSchool: ticket.school})
    if (!serviceApplyData || serviceApplyData.status != 1) {
      return Promise.reject(new GeneralError('No approved service apply'))
    }

    for (let i = 0; i < ticket.serviceData.length; i++) {
      let item = ticket.serviceData[i]
      let {servicePack} = item

      let isNew = false
      let packUser: any = await this.app.service('service-pack-user').Model.findOne({uid, 'snapshot._id': servicePack})
      if (!packUser) {
        isNew = true
        let buyData: any = {
          packId: servicePack,
          order: ticket.order,
        }
        packUser = await this.app.service('service-pack-user').buyByOrder(buyData, {user})
      }
      for (let key in item) {
        if ((key == 'cash' || key == 'point' || key == 'gift') && item[key] > 0) {
          await this.app.service('service-pack-user-data').add(
            {
              packUser: packUser._id,
              type: key,
              times: item[key],
              payMethod: key,
              isNew,
              order: ticket.order,
              serviceTicket: id,
              isClaim: true,
            },
            {user}
          )
        }
      }
      await this.app.service('service-pack-apply').Model.updateOne({_id: serviceApplyData._id}, {$addToSet: {serviceTicket: id}})
      await this.Model.updateOne({_id: id}, {uid})
      await this.app.service('service-pack').Model.updateOne({_id: servicePack}, {$inc: {'count.ticket': -1}})
    }
    return {ok: 1}
  }

  // 取消分配
  async getDisclaim({ids}: any, params: Params): Promise<any> {
    let tickets: any = await this.Model.find({_id: {$in: ids}}).lean()
    for (let i = 0; i < tickets.length; i++) {
      const ticket = tickets[i]
      let {_id, uid, serviceData, order} = ticket
      if (!ticket.uid) {
        return Promise.reject(new GeneralError('Not claimed'))
      }
      await this.Model.updateOne({_id: _id}, {$unset: {uid: ''}})
      for (let j = 0; j < serviceData.length; j++) {
        let {servicePack} = serviceData[j]
        let packUser: any = await this.app.service('service-pack-user').Model.findOne({uid: uid, 'snapshot._id': servicePack})
        let serviceCount = await this.app.service('order').calcServiceCount(packUser._id, order, _id)
        if (serviceCount.unused > 0) {
          await this.app.service('service-pack-user-data').used(
            {
              packUser: packUser._id,
              type: 'refund',
              times: serviceCount.unused,
              order: order,
              serviceTicket: _id,
              isClaim: true,
            },
            params
          )
          await this.app.service('service-pack').Model.updateOne({_id: servicePack}, {$inc: {'count.ticket': 1}})
        }
      }
      await this.app.service('service-pack-apply').Model.updateOne({serviceTicket: _id}, {$pull: {serviceTicket: _id}})
    }
    return {ok: 1}
  }

  // 同步可用次数
  async updateCount({userData}: any, params?: TxnParams): Promise<void> {
    const options = Acan.getTxnOptions(params)
    let tickets: any = []
    let list = []
    for (let i = 0; i < userData.length; i++) {
      const item = userData[i]
      if (item.serviceTicket && !tickets.includes(item.serviceTicket)) {
        tickets.push(item.serviceTicket)
        list.push(item)
      }
    }
    for (let i = 0; i < list.length; i++) {
      const {packUser, order, serviceTicket} = list[i]
      let packUserData: any = await this.app.service('service-pack-user').Model.findOne({_id: packUser}, null, options)
      let serviceCount = await this.app.service('order').calcServiceCount(packUserData._id, order, serviceTicket, params)
      let a = await this.Model.updateOne(
        {_id: serviceTicket, 'serviceData.servicePack': packUserData.snapshot._id},
        {
          'serviceData.$.cash': serviceCount.unusedCash,
          'serviceData.$.point': serviceCount.unusedPoint,
          'serviceData.$.gift': serviceCount.unusedGift,
        },
        options
      )
    }
  }
}
