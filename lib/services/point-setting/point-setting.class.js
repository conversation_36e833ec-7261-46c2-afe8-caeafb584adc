"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PointSetting = void 0;
const feathers_mongoose_1 = require("feathers-mongoose");
const { NotFound, GeneralError, BadRequest } = require('@feathersjs/errors');
class PointSetting extends feathers_mongoose_1.Service {
    //eslint-disable-next-line @typescript-eslint/no-unused-vars
    constructor(options, app) {
        super(options);
        this.app = app;
    }
    /**
     * 获得/使用积分数量计算
     * amount 积分/美分
     * isPoint 传入的amount是否为积分
     */
    async calcPoint({ type, amount, tab = 'claim', isPoint = false }) {
        let setting = await this.Model.findOne({ ...type, tab });
        if (!setting) {
            return {
                success: false,
                message: 'No point setting',
                price: amount,
                point: 0,
            };
        }
        if (setting.mode === 'fixed') {
            return {
                success: true,
                price: 0,
                point: setting.value,
            };
        }
        else {
            let point = 0;
            let num = (setting.value / 100) * (amount / (isPoint ? 1 : 100));
            if (!num) {
                point = 0;
            }
            else if (num <= 1) {
                point = 1;
            }
            else {
                point = Math.floor(num);
            }
            return {
                success: true,
                price: 0,
                point: point,
            };
        }
    }
}
exports.PointSetting = PointSetting;
