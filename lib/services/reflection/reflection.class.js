"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Reflection = void 0;
const feathers_mongoose_1 = require("feathers-mongoose");
class Reflection extends feathers_mongoose_1.Service {
    //eslint-disable-next-line @typescript-eslint/no-unused-vars
    constructor(options, app) {
        super(options);
        this.app = app;
    }
    async getFixedTo() {
        const count = await this.Model.count({ to: { $ne: null }, 'to.0': { $exists: false } }).sort({ _id: -1 });
        const rs = await this.Model.updateMany({ to: { $ne: null }, 'to.0': { $exists: false } }, { $unset: { to: '' } });
        return { rs, count };
    }
    async extUser(one, params) {
        if (!one.uid)
            return;
        one.user = await this.app.service('users').uidToInfo(one.uid);
        if (one.school) {
            const rs = await this.app.service('school-user').getInfo({ email: one.user.email, school: one.school });
            one.user = rs || one.user;
        }
    }
    async extFiles(one, params) {
        if (Acan.isEmpty(one.attach))
            return;
        one.files = await this.app.service('files').userFileInfo({ arr: one.attach, uid: one.uid }, params);
    }
    // 发布到library的数据
    async unitSnapshot(unit, params) {
        const rs = Acan.clone(await this.app.service('reflection').Model.find({ unit, public: true, to: [] }));
        await this.extData(rs, params);
        return rs;
    }
    async extData(data, params) {
        if (Array.isArray(data)) {
            for (const one of data) {
                await this.extUser(one, params);
                await this.extFiles(one, params);
            }
        }
        else {
            await this.extUser(data, params);
            await this.extFiles(data, params);
        }
        return data;
    }
}
exports.Reflection = Reflection;
