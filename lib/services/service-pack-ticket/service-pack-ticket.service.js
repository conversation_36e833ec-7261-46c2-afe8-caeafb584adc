"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const service_pack_ticket_class_1 = require("./service-pack-ticket.class");
const service_pack_ticket_model_1 = __importDefault(require("../../models/service-pack-ticket.model"));
const service_pack_ticket_hooks_1 = __importDefault(require("./service-pack-ticket.hooks"));
function default_1(app) {
    const options = {
        Model: (0, service_pack_ticket_model_1.default)(app),
        paginate: app.get('paginate'),
    };
    // Initialize our service with any options it requires
    app.use('/service-pack-ticket', new service_pack_ticket_class_1.ServicePackTicket(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('service-pack-ticket');
    service.hooks(service_pack_ticket_hooks_1.default);
}
exports.default = default_1;
