"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const school_log_class_1 = require("./school-log.class");
const school_log_model_1 = __importDefault(require("../../models/school-log.model"));
const school_log_hooks_1 = __importDefault(require("./school-log.hooks"));
function default_1(app) {
    const options = {
        Model: (0, school_log_model_1.default)(app),
        paginate: app.get('paginate')
    };
    // Initialize our service with any options it requires
    app.use('/school-log', new school_log_class_1.SchoolLog(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('school-log');
    service.hooks(school_log_hooks_1.default);
}
exports.default = default_1;
