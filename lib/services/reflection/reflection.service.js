"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const reflection_class_1 = require("./reflection.class");
const reflection_model_1 = __importDefault(require("../../models/reflection.model"));
const reflection_hooks_1 = __importDefault(require("./reflection.hooks"));
function default_1(app) {
    const options = {
        Model: (0, reflection_model_1.default)(app),
        whitelist: ['$exists'],
        paginate: app.get('paginate'),
    };
    // Initialize our service with any options it requires
    app.use('/reflection', new reflection_class_1.Reflection(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('reflection');
    service.hooks(reflection_hooks_1.default);
}
exports.default = default_1;
