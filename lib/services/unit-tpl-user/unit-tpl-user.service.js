"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const unit_tpl_user_class_1 = require("./unit-tpl-user.class");
const unit_tpl_user_model_1 = __importDefault(require("../../models/unit-tpl-user.model"));
const unit_tpl_user_hooks_1 = __importDefault(require("./unit-tpl-user.hooks"));
function default_1(app) {
    const options = {
        Model: (0, unit_tpl_user_model_1.default)(app),
        paginate: app.get('paginate')
    };
    // Initialize our service with any options it requires
    app.use('/unit-tpl-user', new unit_tpl_user_class_1.UnitTplUser(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('unit-tpl-user');
    service.hooks(unit_tpl_user_hooks_1.default);
}
exports.default = default_1;
