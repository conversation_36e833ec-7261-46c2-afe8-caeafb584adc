"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const session_takeaway_snapshot_class_1 = require("./session-takeaway-snapshot.class");
const session_takeaway_snapshot_model_1 = __importDefault(require("../../models/session-takeaway-snapshot.model"));
const session_takeaway_snapshot_hooks_1 = __importDefault(require("./session-takeaway-snapshot.hooks"));
function default_1(app) {
    const options = {
        Model: (0, session_takeaway_snapshot_model_1.default)(app),
        paginate: app.get('paginate')
    };
    // Initialize our service with any options it requires
    app.use('/session-takeaway-snapshot', new session_takeaway_snapshot_class_1.SessionTakeawaySnapshot(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('session-takeaway-snapshot');
    service.hooks(session_takeaway_snapshot_hooks_1.default);
}
exports.default = default_1;
