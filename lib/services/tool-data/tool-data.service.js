"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const tool_data_class_1 = require("./tool-data.class");
const tool_data_model_1 = __importDefault(require("../../models/tool-data.model"));
const tool_data_hooks_1 = __importDefault(require("./tool-data.hooks"));
function default_1(app) {
    const options = {
        Model: (0, tool_data_model_1.default)(app),
        paginate: app.get('paginate')
    };
    // Initialize our service with any options it requires
    app.use('/tool-data', new tool_data_class_1.ToolData(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('tool-data');
    service.hooks(tool_data_hooks_1.default);
}
exports.default = default_1;
