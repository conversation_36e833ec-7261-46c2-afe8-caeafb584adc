"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const service_pack_user_logs_class_1 = require("./service-pack-user-logs.class");
const service_pack_user_logs_model_1 = __importDefault(require("../../models/service-pack-user-logs.model"));
const service_pack_user_logs_hooks_1 = __importDefault(require("./service-pack-user-logs.hooks"));
function default_1(app) {
    const options = {
        Model: (0, service_pack_user_logs_model_1.default)(app),
        paginate: app.get('paginate')
    };
    // Initialize our service with any options it requires
    app.use('/service-pack-user-logs', new service_pack_user_logs_class_1.ServicePackUserLogs(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('service-pack-user-logs');
    service.hooks(service_pack_user_logs_hooks_1.default);
}
exports.default = default_1;
