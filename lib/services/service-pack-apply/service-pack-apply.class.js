"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServicePackApply = void 0;
const feathers_mongoose_1 = require("feathers-mongoose");
const { NotFound, GeneralError, BadRequest } = require('@feathersjs/errors');
class ServicePackApply extends feathers_mongoose_1.Service {
    //eslint-disable-next-line @typescript-eslint/no-unused-vars
    constructor(options, app) {
        super(options);
        this.app = app;
    }
    async extServicePack(one, params) {
        one.servicePackInfo = await this.app.service('service-pack').Model.findOne({ _id: one.servicePack });
    }
    async extUser(one, params) {
        if (one.uid) {
            one.userInfo = await this.app.service('users').uidToInfo(one.uid);
        }
        if (one.follower && !one.schoolOfFollower) {
            one.followerInfo = await this.app.service('users').uidToInfo(one.follower);
        }
        if (one.follower && one.schoolOfFollower) {
            one.followerInfo = await this.app.service('school-user').Model.findOne({ uid: one.follower, school: one.schoolOfFollower });
        }
    }
    async extSchool(one, params) {
        if (one.sharedSchool) {
            one.schoolInfo = await this.app.service('school-plan').Model.findOne({ _id: one.sharedSchool });
        }
    }
    async extSchoolPrice(one, params) {
        if (one.sharedSchool) {
            one.schoolPriceInfo = await this.app.service('service-pack-school-price').Model.findOne({ school: one.sharedSchool, servicePack: one.servicePack });
        }
    }
    async getCountType(query, params) {
        return await this.Model.aggregate([
            { $match: {} },
            {
                $group: {
                    _id: {
                        mentoringType: '$mentoringType',
                        status: '$status',
                    },
                    count: { $sum: 1 },
                },
            },
        ]);
    }
    async getCount({ sharedSchool, status, servicePack, archive }, params) {
        let query = { status, servicePack };
        if (archive !== undefined) {
            query.archive = archive;
        }
        if (sharedSchool !== undefined) {
            query.sharedSchool = sharedSchool;
        }
        let within = await this.Model.count({ ...query, withinSchool: true });
        let outside = await this.Model.count({ ...query, withinSchool: false });
        return {
            within,
            outside,
        };
    }
    async updateOrderInfo({ uid, servicePack, sharedSchool, order }) {
        let serviceApplyData = await this.Model.findOne({ uid, servicePack: servicePack, sharedSchool });
        if ((serviceApplyData === null || serviceApplyData === void 0 ? void 0 : serviceApplyData.status) == 1) {
            return await this.Model.updateOne({ _id: serviceApplyData._id }, { $addToSet: { order } });
        }
    }
    async handleEmail(result, data, params) {
        let { servicePack, sharedSchool } = result;
        let { status, interviewInvited } = data;
        // 通过邮件
        if (status == 1) {
            let servicePackData = await this.app.service('service-pack').Model.findOne({ _id: servicePack });
            let servicePriceData = await this.app.service('service-pack-school-price').Model.findOne({ school: sharedSchool, servicePack: servicePack });
            if ((servicePackData.salesTarget.indexOf('personal') > -1 && !sharedSchool) || ((servicePriceData === null || servicePriceData === void 0 ? void 0 : servicePriceData.priceEnable) && sharedSchool)) {
                // 有购买邮件
                this.send(result, 'ReminderOfApplicationApprovalWithOrder', params);
            }
            else {
                // 无购买邮件
                this.send(result, 'ReminderOfApplicationApprovalWithoutOrder', params);
            }
        }
        // 拒绝邮件
        if (status == -1) {
            this.send(result, 'ReminderOfApplicationRejection', params);
        }
        // 面试邮件
        if (interviewInvited) {
            this.send(result, 'ReminderOfInterview', params);
        }
    }
    async send(doc, tpl, params) {
        var _a;
        const { uid, email, servicePack, sharedSchool } = doc;
        let user = {
            _id: uid,
            email: email,
        };
        let userData = await this.app.service('users').uidToInfo(uid);
        let servicePackData = await this.app.service('service-pack').Model.findOne({ _id: servicePack });
        let url = '';
        let url2 = '';
        if (tpl === 'ReminderOfInterview') {
            url = `${SiteUrl}/v2/service/pack/${servicePack}`;
            if (sharedSchool) {
                url += `?sharedSchool=${sharedSchool}`;
            }
        }
        else if (tpl === 'ReminderOfApplicationApprovalWithOrder') {
            url = `${SiteUrl}/v2/service/pack/${servicePack}`;
            if (sharedSchool) {
                url += `?sharedSchool=${sharedSchool}`;
            }
            url2 = `${SiteUrl}/v2/`;
        }
        else if (tpl === 'ReminderOfApplicationApprovalWithoutOrder') {
            url = `${SiteUrl}/v2/service/pack/${servicePack}`;
            if (sharedSchool) {
                url += `?sharedSchool=${sharedSchool}`;
            }
        }
        else if (tpl === 'ReminderOfApplicationRejection') {
            url = `${SiteUrl}/v2/premcpack/enroll/${servicePack}`;
            if (sharedSchool) {
                url += `?sharedSchool=${sharedSchool}`;
            }
        }
        return await this.app.service('notice-tpl').mailto(tpl, user, { username: userData.name.join(' '), name: servicePackData.name, url, url2 }, (_a = params.user) === null || _a === void 0 ? void 0 : _a._id);
    }
    // 面试失败/过期 手动return
    async getInterviewReturn({ id }) {
        let serviceApplyData = await this.Model.findOne({ _id: id });
        let { interviewInvited, takeaway, takeawayId } = serviceApplyData;
        // if (!interviewInvited) {
        //   return Promise.reject(new GeneralError('The status of the application can not be returned.'))
        // }
        // takeaway解绑
        let takeawayData = await this.app.service('session-takeaway-snapshot').Model.findOne({ _id: takeawayId });
        let session = await this.app.service('session').Model.findOne({ _id: takeawayData.session });
        if (session === null || session === void 0 ? void 0 : session.booking) {
            await this.app.service('service-booking').Model.updateOne({ _id: session.booking }, {
                $unset: { session: 1, servicePackApply: 1 },
            });
        }
        if (session) {
            await this.app.service('session').remove(session._id);
        }
        return await this.Model.updateOne({ _id: id }, {
            interviewStatus: 0,
            interviewInvited: false,
            interviewApply: false,
            $unset: { interviewOrder: '', takeaway: '', takeawayId: '', takeawayCreatedAt: '', interviewPurchaseExpireAt: '' },
        });
    }
    async cron1({}, params) {
        this.autoReturnInterviewPurchaseExpire();
    }
    // 面试购买过期 自动return
    async autoReturnInterviewPurchaseExpire() {
        this.Model.find({ status: 0, interviewPurchaseExpireAt: { $lt: Date.now() }, interviewOrder: { $exists: false } }).then(async (rs) => {
            for (let i = 0; i < rs.length; i++) {
                const item = rs[i];
                await this.Model.updateOne({ _id: item._id }, {
                    interviewInvited: false,
                    interviewPurchaseExpired: true,
                    $unset: { interviewPurchaseExpireAt: '' },
                });
            }
        });
    }
    async getGroupByFollower({}, params) {
        let match = {
            follower: { $exists: true },
        };
        let list = await this.Model.aggregate([
            { $match: match },
            {
                $group: {
                    _id: { follower: '$follower', schoolOfFollower: '$schoolOfFollower' },
                    follower: { $first: '$follower' },
                    schoolOfFollower: { $first: '$schoolOfFollower' },
                    count: { $sum: 1 },
                },
            },
            { $sort: { count: -1 } },
        ]);
        for (let i = 0; i < list.length; i++) {
            await this.extUser(list[i]);
        }
        return list;
    }
    // session结束后,获取takeaway,更新apply面试状态 #5538
    async updateTakeaway({ session, snapshot }) {
        let { students } = snapshot;
        for (let i = 0; i < students.length; i++) {
            const student = students[i];
            let booking = await this.app.service('service-booking').Model.findOne({ 'session._id': session, booker: student.uid });
            let takeaway = await this.app.service('session-takeaway-snapshot').Model.findOne({ session, uid: student.uid });
            const takeawayUrl = `/account/takeaway/${takeaway === null || takeaway === void 0 ? void 0 : takeaway.session}/view/${student === null || student === void 0 ? void 0 : student.uid}`;
            await this.Model.updateOne({ _id: booking.servicePackApply }, {
                interviewStatus: 1,
                takeaway: takeawayUrl,
                takeawayId: takeaway === null || takeaway === void 0 ? void 0 : takeaway._id,
                takeawayCreatedAt: takeaway === null || takeaway === void 0 ? void 0 : takeaway.createdAt,
            });
        }
    }
}
exports.ServicePackApply = ServicePackApply;
