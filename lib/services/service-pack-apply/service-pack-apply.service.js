"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const service_pack_apply_class_1 = require("./service-pack-apply.class");
const service_pack_apply_model_1 = __importDefault(require("../../models/service-pack-apply.model"));
const service_pack_apply_hooks_1 = __importDefault(require("./service-pack-apply.hooks"));
function default_1(app) {
    const options = {
        Model: (0, service_pack_apply_model_1.default)(app),
        whitelist: ['$regex', '$exists', '$elemMatch'],
        paginate: app.get('paginate'),
    };
    // Initialize our service with any options it requires
    app.use('/service-pack-apply', new service_pack_apply_class_1.ServicePackApply(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('service-pack-apply');
    service.hooks(service_pack_apply_hooks_1.default);
}
exports.default = default_1;
