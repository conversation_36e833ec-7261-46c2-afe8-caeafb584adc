"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const unit_class_1 = require("./unit.class");
const unit_model_1 = __importDefault(require("../../models/unit.model"));
const unit_hooks_1 = __importDefault(require("./unit.hooks"));
function default_1(app) {
    const options = {
        Model: (0, unit_model_1.default)(app),
        whitelist: ['$exists', '$regex', '$options'],
        paginate: app.get('paginate'),
    };
    // Initialize our service with any options it requires
    app.use('/unit', new unit_class_1.Unit(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('unit');
    service.hooks(unit_hooks_1.default);
}
exports.default = default_1;
