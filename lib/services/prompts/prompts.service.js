"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const prompts_class_1 = require("./prompts.class");
const prompts_model_1 = __importDefault(require("../../models/prompts.model"));
const prompts_hooks_1 = __importDefault(require("./prompts.hooks"));
function default_1(app) {
    const options = {
        Model: (0, prompts_model_1.default)(app),
        whitelist: ['$regex', '$options', '$text', '$search'],
        paginate: app.get('paginate'),
    };
    // Initialize our service with any options it requires
    app.use('/prompts', new prompts_class_1.Prompts(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('prompts');
    service.hooks(prompts_hooks_1.default);
}
exports.default = default_1;
