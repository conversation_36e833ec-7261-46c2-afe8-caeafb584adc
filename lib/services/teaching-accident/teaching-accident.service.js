"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const teaching_accident_class_1 = require("./teaching-accident.class");
const teaching_accident_model_1 = __importDefault(require("../../models/teaching-accident.model"));
const teaching_accident_hooks_1 = __importDefault(require("./teaching-accident.hooks"));
function default_1(app) {
    const options = {
        Model: (0, teaching_accident_model_1.default)(app),
        paginate: app.get('paginate'),
    };
    // Initialize our service with any options it requires
    app.use('/teaching-accident', new teaching_accident_class_1.TeachingAccident(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('teaching-accident');
    service.hooks(teaching_accident_hooks_1.default);
}
exports.default = default_1;
