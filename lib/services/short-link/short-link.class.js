"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ShortLink = void 0;
const feathers_mongoose_1 = require("feathers-mongoose");
const { NotFound, GeneralError, BadRequest } = require('@feathersjs/errors');
class ShortLink extends feathers_mongoose_1.Service {
    //eslint-disable-next-line @typescript-eslint/no-unused-vars
    constructor(options, app) {
        super(options);
        this.app = app;
    }
    async getShortLink({ url }, params) {
        let code = Acan.MD5(url).substr(0, 6);
        let data = await this.Model.findOne({ code: code });
        if (data) {
            return data;
        }
        else {
            let data = await this.Model.create({ url: url, code: code, shortUrl: `${SiteUrl}/j/${code}` });
            return data;
        }
    }
}
exports.ShortLink = ShortLink;
