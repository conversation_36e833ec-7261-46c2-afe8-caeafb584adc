"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const task_outline_class_1 = require("./task-outline.class");
const task_outline_model_1 = __importDefault(require("../../models/task-outline.model"));
const task_outline_hooks_1 = __importDefault(require("./task-outline.hooks"));
function default_1(app) {
    const options = {
        Model: (0, task_outline_model_1.default)(app),
        paginate: app.get('paginate')
    };
    // Initialize our service with any options it requires
    app.use('/task-outline', new task_outline_class_1.TaskOutline(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('task-outline');
    service.hooks(task_outline_hooks_1.default);
}
exports.default = default_1;
