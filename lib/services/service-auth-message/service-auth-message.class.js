"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServiceAuthMessage = void 0;
const feathers_mongoose_1 = require("feathers-mongoose");
const { NotFound, GeneralError, BadRequest } = require('@feathersjs/errors');
class ServiceAuthMessage extends feathers_mongoose_1.Service {
    //eslint-disable-next-line @typescript-eslint/no-unused-vars
    constructor(options, app) {
        super(options);
        this.app = app;
    }
    async extUser(one, params) {
        one.userInfo = await this.app.service('users').Model.findOne({ _id: one.uid });
    }
}
exports.ServiceAuthMessage = ServiceAuthMessage;
