"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const short_link_class_1 = require("./short-link.class");
const short_link_model_1 = __importDefault(require("../../models/short-link.model"));
const short_link_hooks_1 = __importDefault(require("./short-link.hooks"));
function default_1(app) {
    const options = {
        Model: (0, short_link_model_1.default)(app),
        paginate: app.get('paginate'),
    };
    // Initialize our service with any options it requires
    app.use('/short-link', new short_link_class_1.ShortLink(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('short-link');
    service.hooks(short_link_hooks_1.default);
}
exports.default = default_1;
