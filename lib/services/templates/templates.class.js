"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Templates = void 0;
const feathers_knex_1 = require("feathers-knex");
const logger_1 = __importDefault(require("../../logger"));
class Templates extends feathers_knex_1.Service {
    //eslint-disable-next-line @typescript-eslint/no-unused-vars
    constructor(options, app) {
        super({
            ...options,
            name: 'ta_templates',
        });
        this.app = app;
    }
    async getOldList({ type, name }, params) {
        const knexj = this.app.get('knexClientj');
        const con = { 'tpl.del_flag': 0, 'tpl.category': 1 };
        if (type)
            con['tags.tag_type'] = type;
        const qb = knexj('ta_templates as tpl').leftJoin('ta_templates_tags as tags', 'tags.template_id', 'tpl.id').where(con);
        if (name)
            qb.whereILike('tpl.name', `%${name}%`);
        const data = await qb.orderBy('tpl.update_time', 'desc').limit(30);
        return { data };
    }
    async getDict({ code, ttl }) {
        return await this.app.get('redisCache')(`old:${code}`, async () => {
            const knexj = this.app.get('knexClientj');
            const trr = await knexj('sys_dict as dict')
                .where({ dict_code: code })
                .leftJoin('sys_dict_item as item', 'item.dict_id', 'dict.id')
                .select('item_value as id', 'item_text as value')
                .orderBy('sort_order', 'asc');
            const list = {};
            trr.map((v) => {
                list[v.id] = v.value;
            });
            logger_1.default.warn('getDict:', code, ttl, list);
            return list;
        }, ttl || 600);
    }
    async getQuickList({}, params) {
        var _a;
        const knexj = this.app.get('knexClientj');
        const { ttl } = (_a = params.query) !== null && _a !== void 0 ? _a : {};
        const typeConf = await this.getDict({ code: 'prompt_type', ttl });
        const purposeConf = await this.getDict({ code: 'prompt_purpose', ttl });
        const rs = await knexj('ta_templates')
            .where({ category: 2, del_flag: 0 })
            .limit(200)
            .select('id', 'name', 'presentation_id ', 'cover', 'page_object_ids as pageIds');
        const ids = rs.map((v) => v.id);
        const tagRs = await knexj('ta_templates_tags').whereIn('template_id', ids).select('template_id as id', 'tag_id', 'tag_type');
        const list = {};
        tagRs.map((v) => {
            if (!list[v.id])
                list[v.id] = { purpose: [], tags: [] };
            if (v.tag_type === 4)
                list[v.id].tags.push(typeConf[v.tag_id]);
            if (v.tag_type === 5)
                list[v.id].purpose.push(purposeConf[v.tag_id] || v.tag_id);
            return v;
        });
        rs.map((v) => {
            var _a, _b;
            if (list[v.id])
                Object.assign(v, list[v.id]);
            v.pageIds = (_b = (_a = v.pageIds) === null || _a === void 0 ? void 0 : _a.split(',')) !== null && _b !== void 0 ? _b : [];
        });
        return { purpose: purposeConf, type: typeConf, data: rs };
    }
}
exports.Templates = Templates;
