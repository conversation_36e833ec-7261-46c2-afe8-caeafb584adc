"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UnitTpl = void 0;
const feathers_mongoose_1 = require("feathers-mongoose");
const bson_1 = require("bson");
const logger_1 = __importDefault(require("../../logger"));
class UnitTpl extends feathers_mongoose_1.Service {
    //eslint-disable-next-line @typescript-eslint/no-unused-vars
    constructor(options, app) {
        super(options);
        this.app = app;
    }
    getName({ _id }, params) {
        return this.Model.findById(_id).select('name');
    }
    getPdTask({}, params) {
        return this.Model.findOne({ mode: 'pdTask', uid: '1' });
    }
    getPdUnit({}, params) {
        return this.Model.findOne({ mode: 'pdUnit', uid: '1' });
    }
    getTask({ curriculum }, params) {
        return this.Model.findOne({ mode: curriculum === 'pd' ? 'pdTask' : 'task', curriculum, school: '1' });
    }
    getUnit({ curriculum }, params) {
        return this.Model.findOne({ mode: curriculum === 'pd' ? 'pdUnit' : 'unit', curriculum, school: '1' });
    }
    getCopyTpl(query, params) {
        return this.copyTpl(query, params);
    }
    async copyTpl({ curricId, school }, params) {
        var _a, _b;
        const group = new bson_1.ObjectID().toString();
        const { code } = (await this.app.service('curric').Model.findById(curricId).select('code')) || {};
        const curriculum = code || 'others';
        let doc = Acan.clone(await this.getUnit({ curriculum }, params));
        if (!doc)
            return Promise.reject(new Error('curriculum code not found unit: ' + curriculum));
        const rs = {};
        if (code === 'pd') {
            rs.unit = { _id: doc._id };
        }
        else {
            doc.school = school || ((_a = params.user) === null || _a === void 0 ? void 0 : _a._id);
            doc.group = group;
            doc.curricId = curricId;
            for (const k of ['createdAt', 'updatedAt', '_id']) {
                delete doc[k];
            }
            rs.unit = await this.create(doc);
        }
        doc = Acan.clone(await this.getTask({ curriculum }, params));
        if (!doc)
            return Promise.reject(new Error('curriculum code not found task: ' + curriculum));
        if (code === 'pd') {
            rs.task = { _id: doc._id };
        }
        else {
            doc.school = school || ((_b = params.user) === null || _b === void 0 ? void 0 : _b._id);
            doc.group = group;
            doc.curricId = curricId;
            for (const k of ['createdAt', 'updatedAt', '_id']) {
                delete doc[k];
            }
            rs.task = await this.create(doc);
        }
        return rs;
    }
    async mergePubTpl(doc) {
        const rs = this.app.get(`${doc.mode.toFirstUpperCase()}Tpl`);
        if (!rs)
            return logger_1.default.error('Not found pub tpl in mode:' + doc.mode);
        logger_1.default.info(`Found mode: ${doc.mode} pub tpl`, rs.length);
        const list = {};
        for (const o of rs) {
            list[o.code] = o;
        }
        for (const o of doc.data) {
            if (list[o.code])
                Object.assign(o, Object.assign(list[o.code], o));
        }
    }
    async snapshot(_id) {
        logger_1.default.info('unit-tpl.snapshot', _id);
        if (!_id)
            return {};
        const doc = Acan.clone(await this.Model.findById(_id));
        if (!doc)
            return {};
        const tags = {};
        const types = ['checkbox', 'choice', 'choice-mark'];
        let $in = doc.data.filter((v) => v.enable && !v.code && !v.tags && types.includes(v.type)).map((v) => v.name);
        if (!Acan.isEmpty($in)) {
            const arr = Acan.clone(await this.app.service('tags').Model.find({ uid: '1', set: $in }).select('snapshot'));
            for (const o of arr) {
                if (!o.snapshot)
                    continue;
                tags[o.set] = o.snapshot.child;
            }
        }
        $in = doc.data.filter((v) => v.enable && v.tags).map((v) => v.tags);
        if (!Acan.isEmpty($in)) {
            const arr = Acan.clone(await this.app.service('tags').Model.find({ _id: $in }).select('snapshot'));
            for (const o of arr) {
                if (!o.snapshot)
                    continue;
                tags[o._id] = o.snapshot.child;
            }
        }
        const info = Acan.clone(doc);
        delete info.data;
        await this.mergePubTpl(doc);
        return {
            tags,
            template: doc.data,
            templateInfo: info,
        };
    }
    async getSnapshot({ _id }, params) {
        return this.snapshot(_id);
    }
    async patchSetDefault({ school, group }, params) {
        var _a;
        const uid = (_a = params.user) === null || _a === void 0 ? void 0 : _a._id.toString();
        const key = uid === school ? 'personal' : school;
        const confDocs = Acan.clone(await this.app.service('conf-user').Model.find({ uid, key: { $in: ['UnitTplDefault', 'TaskTplDefault'] } }));
        const conf = {};
        const keyMap = { TaskTplDefault: 'task', UnitTplDefault: 'unit' };
        confDocs.map((v) => {
            conf[keyMap[v.key]] = v._id;
        });
        const arr = Acan.clone(await this.Model.find({ group }).select('mode curricId curriculum'));
        const rs = [];
        for (const o of arr) {
            const $set = { [`val.${key}`]: o._id };
            rs.push(await this.app.service('conf-user').Model.updateOne({ _id: conf[o.mode] }, { $set }));
        }
        return { ok: 1, rs };
    }
}
exports.UnitTpl = UnitTpl;
