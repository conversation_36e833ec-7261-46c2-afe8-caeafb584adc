"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const user_notice_setting_class_1 = require("./user-notice-setting.class");
const user_notice_setting_model_1 = __importDefault(require("../../models/user-notice-setting.model"));
const user_notice_setting_hooks_1 = __importDefault(require("./user-notice-setting.hooks"));
function default_1(app) {
    const options = {
        Model: (0, user_notice_setting_model_1.default)(app),
        paginate: app.get('paginate'),
    };
    // Initialize our service with any options it requires
    app.use('/user-notice-setting', new user_notice_setting_class_1.UserNoticeSetting(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('user-notice-setting');
    service.hooks(user_notice_setting_hooks_1.default);
}
exports.default = default_1;
