"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const session_snapshot_class_1 = require("./session-snapshot.class");
const session_snapshot_model_1 = __importDefault(require("../../models/session-snapshot.model"));
const session_snapshot_hooks_1 = __importDefault(require("./session-snapshot.hooks"));
function default_1(app) {
    const options = {
        Model: (0, session_snapshot_model_1.default)(app),
        whitelist: ['$exists'],
        paginate: app.get('paginate'),
    };
    // Initialize our service with any options it requires
    app.use('/session-snapshot', new session_snapshot_class_1.SessionSnapshot(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('session-snapshot');
    service.hooks(session_snapshot_hooks_1.default);
}
exports.default = default_1;
