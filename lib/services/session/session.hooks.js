"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const authentication = __importStar(require("@feathersjs/authentication"));
// Don't remove this comment. It's needed to format import lines nicely.
const { authenticate } = authentication.hooks;
const search = require('feathers-mongodb-fuzzy-search');
const hook_1 = __importDefault(require("../../hook"));
const logger_1 = __importDefault(require("../../logger"));
const errors_1 = require("@feathersjs/errors");
function logQuery(query) {
    const logRs = Acan.clone(query);
    delete logRs.$select;
    logger_1.default.info(logRs);
}
exports.default = {
    before: {
        all: [],
        find: [
            hook_1.default.userQuery(),
            async (d) => {
                var _a, _b, _c, _d, _e;
                const query = (_a = d.params.query) !== null && _a !== void 0 ? _a : {};
                let $substitute = query.$substitute;
                delete query.$substitute;
                if (query.sid)
                    return d;
                query.del = (_b = query.del) !== null && _b !== void 0 ? _b : false;
                // if (!query.del) query.status = query.status ?? {$ne: 'close'}
                query.$select = Acan.clone((_c = query.$select) !== null && _c !== void 0 ? _c : d.service.selectList);
                if (d.params.inside)
                    return;
                const uid = (_d = d.params.user) === null || _d === void 0 ? void 0 : _d._id;
                if (query.type === 'courses')
                    query.$select.push('overview', 'childs');
                if (Acan.isObjectId(query._id) || ((_e = query._id) === null || _e === void 0 ? void 0 : _e.$in)) {
                    delete query.status;
                    return d;
                }
                query.$or = Array.isArray(query.$or) ? query.$or : [];
                if (query['subjects.session']) {
                    query.$or.push({ 'task.outline.outline.subjects': query['subjects.session'] });
                    query.$or.push({ 'task.outline.assess.subjects': query['subjects.session'] });
                    delete query['subjects.session'];
                }
                else if (query['subjects.pd']) {
                    query.$or.push({ 'task.outline.outline.subjects': query['subjects.pd'] });
                    query.$or.push({ 'task.outline.assess.subjects': query['subjects.pd'] });
                    // query.$or.push({'task.outline.pd.subjects': query['subjects.pd']})
                    // query.$or.push({'task.outline.assess.subjects': query['subject.pd']})
                    delete query['subjects.pd'];
                }
                if (Acan.isEmpty(query.$or))
                    delete query.$or;
                d.service.queryDate(query, d.params);
                // 公开的，匿名查询用
                if (query.isLib && !query.school) {
                    // for library find
                    Object.assign(query, { school: { $exists: false } });
                    if (query.type !== 'selfStudy') {
                        // 公共的自学习课堂,无需过滤时间
                        delete query.start;
                        delete query.end;
                        delete query.uid;
                    }
                    else if (query.type !== 'selfStudy') {
                        // 非自学习课堂需要限制报名截至时间
                        query.regDate = { $gt: new Date() };
                    }
                    // self-study not end
                    // if (query.type === 'taskWorkshop' && query.sessionType === 'student') delete query.end
                    if (query.sessionType === 'student')
                        delete query.end;
                    delete query.isLib;
                    // if (uid) query.uid = {$ne: uid}
                    if (query.pid) {
                        // child find
                        if (query.pid['$exists'] !== false)
                            delete query.uid;
                    }
                    else
                        query.pid = { $exists: false };
                    if (uid) {
                        // new prompt 页面获取推广课数据，需要能获取自己或者别人的数据
                        if (query.tab == 'other') {
                            query.uid = { $ne: uid };
                        }
                        else if (query.tab === 'me') {
                            query.uid = uid;
                        }
                    }
                    logger_1.default.info('session libary find', uid);
                    logQuery(query);
                    return;
                }
                if (!uid)
                    return;
                // 查询和自己相关的
                // find self workshop
                if (['workshop', 'pdCourses'].includes(query.type) && uid) {
                    if (!query.$select.includes('reg'))
                        query.$select.push('reg');
                }
                // await authenticate('jwt')(d)
                if (query.$sys) {
                    if (!hook_1.default.roleHas(['sys', 'admin'])(d))
                        return (d.result = null), d;
                    delete query.$sys;
                    query.$select.push('students');
                    // if (query.uid === uid) delete query.uid
                }
                else {
                    // View library, non-self data
                    if (query.isLib)
                        delete query.isLib; //, query.uid = {$ne: uid}
                    // view my register
                    else if (query['reg._id']) {
                        if (!hook_1.default.roleHas(['sys', 'admin'])(d)) {
                            query['reg._id'] = uid;
                            delete query.del;
                        }
                        // view my class session
                    }
                    else if (query.students) {
                        if (!hook_1.default.roleHas(['sys', 'admin'])(d)) {
                            query.students = uid;
                            // delete query.del
                        }
                    }
                    // view my data
                    else if (!query.school && !query.classId) {
                        if (!hook_1.default.roleHas(['sys', 'admin'])(d) || !query.uid) {
                            if (!$substitute) {
                                query.uid = uid;
                            }
                        }
                    }
                }
                if (query.pid && query.pid['$exists'] !== false)
                    delete query.uid;
                logQuery(query);
            },
            search({
                fields: ['name'],
            }),
        ],
        get: [
            hook_1.default.toClass,
            async (d) => {
                if (hook_1.default.classExist(d))
                    return d;
                if (/^[\w\d]{5,10}$/.test(d.id + '')) {
                    d.result = Acan.clone(await d.service.Model.findOne({ sid: d.id }));
                }
                return d;
            },
        ],
        create: [
            authenticate('jwt'),
            async (d) => {
                var _a, _b, _c, _d, _e, _f;
                const { booking } = d.data;
                // booking 重复排课 https://github.com/zran-nz/bug/issues/6081
                if (booking) {
                    const rs = await d.app.service('service-booking').Model.findById(booking).select('session');
                    if (rs.session)
                        return Promise.reject(new errors_1.BadRequest('bookingHasImport', { booking }));
                }
                // if (d.params.inside) return
                if (!d.data.regDate && d.data.sessionType === 'live' && Acan.isEmpty(d.data.students))
                    d.data.regDate = d.data.start;
                let { id, cid, pages } = d.data;
                if (!d.data.isAutoCreate)
                    d.data.uid = (_a = d.params.user) === null || _a === void 0 ? void 0 : _a._id;
                if (d.data.childs)
                    d.data.childSize = d.data.childs.length;
                // live课自动设置end时间
                if ((!d.data.status || d.data.status === 'live') && !d.data.end)
                    d.data.end = new Date(Date.now() + 45 * 60000);
                // load pages form unit
                // related get slides.id
                if (cid && Acan.isEmpty(id)) {
                    const unitDoc = await d.app.service('unit').Model.findById(cid).select(['sid']); // , 'snapshot.pages'
                    // if (Acan.isEmpty(pages)) pages = unitDoc.snapshot?.pages
                    if (unitDoc.sid)
                        id = unitDoc.sid;
                }
                if (pages) {
                    if (!d.data.task)
                        d.data.task = { pages };
                    else
                        d.data.task.pages = pages;
                }
                // task snapshot
                const { school, classId } = d.data;
                if (cid) {
                    d.data.task = await d.app.service('unit').snapshot({ _id: cid, school, classId, session: true }, d.params);
                    for (const key of ['questions', 'materials']) {
                        delete d.data.task[key];
                    }
                }
                if (d.data.type.includes('video')) {
                    // 获取video快照数据
                    if (!d.data.task.video)
                        return (d.result = { message: `Not find video, task: ${cid}, video:${d.data.task.video}` }), d;
                    d.data.video = await d.app.service('interactive-videoes').Model.findById(d.data.task.video);
                }
                else if (id && !id.includes(':') && id !== 'disable') {
                    // 获取ppt数据 load slides pages data, 排除购买和复制的课件
                    let rs = await d.app.service('slides').Model.findOne({ id }).select(['pages', 'rev']);
                    if (!rs) {
                        if (!d.data.guest || Acan.isEmpty(d.data.pageIds))
                            return (d.result = { message: `Not find slides, ${id}` }), d;
                        rs = await d.app.service('slides').create({ id, pages: [{ _id: d.data.pageIds[0], url: d.data.image }] });
                    }
                    if ((_b = d.data.task) === null || _b === void 0 ? void 0 : _b.pages)
                        d.data.task.pages = rs.pages || [];
                    else
                        d.data.task = { pages: rs.pages || [] };
                    d.data.rev = rs.rev;
                    delete d.data.pageIds;
                }
                // if (d.data.classId) {
                //   const [rs] = await knexj('cc_school_class').where({id: d.data.classId}).limit(1).select('name', 'school_id')
                //   if (rs) {
                //     d.data.className = rs.name
                //     if (rs.school_id !== '0') d.data.school = rs.school_id
                //     // todo 广播站内信给班级的成员
                //   }
                // }
                d.data.pageNum = (_d = (_c = d.data.pages) === null || _c === void 0 ? void 0 : _c.length) !== null && _d !== void 0 ? _d : 0;
                if (!d.data.image)
                    d.data.image = (_f = (_e = d.data.pages) === null || _e === void 0 ? void 0 : _e[0]) === null || _f === void 0 ? void 0 : _f.url;
                if (!d.data.sid)
                    d.data.sid = 'C00' + Acan.base62encode(Date.now() % 10000000000);
                if (d.data.zoom) {
                    const rs = await d.app.service('zoom-meet').create({ sid: d.data.sid, start: d.data.start, name: d.data.name, ...d.data.zoom }, d.params);
                    if (rs)
                        d.data.zoom = rs;
                    // else delete d.data.zoom
                }
                // self-study session, need archived historically, conflict with create new
                // const {type, status, sessionType} = d.data
                // if (type === 'taskWorkshop' && status === 'student' && sessionType === 'student') {
                //   await d.service.Model.updateMany({cid: d.data.cid, type, status, sessionType, del: false}, {$set: {del: true}})
                // }
            },
        ],
        update: [hook_1.default.disable],
        patch: [
            authenticate('jwt'),
            hook_1.default.toClass,
            async (d) => {
                var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o;
                if (hook_1.default.classExist(d))
                    return d;
                const { guest, sid, status } = d.data;
                // 自动加上结束课堂的时间
                if (status === 'close')
                    d.data.ended = new Date();
                if (d.params.inside)
                    return d;
                // 当课堂已经进入过学生的时候，不能开启匿名登录
                if (Acan.isDefined(guest) && sid) {
                    const room = await d.app.service('rooms').findOne({ sid }).select(['members._id']);
                    if (!Acan.isEmpty(room.members))
                        delete d.data.guest;
                    delete d.data.sid;
                }
                // 检查报名人数是否超额
                // check regMax for enroll
                if ((_b = (_a = d.data) === null || _a === void 0 ? void 0 : _a.$addToSet) === null || _b === void 0 ? void 0 : _b.reg) {
                    const rs = await d.service.Model.findOne({ _id: d.id }).select(['regMax', 'reg']);
                    if (rs.regMax && rs.regMax < rs.reg.length) {
                        return Promise.reject(new Error('Registration is full'));
                    }
                }
                // substitute
                let origin = await d.service.Model.findOne({ _id: d.id }).lean();
                d.params.$origin = origin;
                let { substituteTeacherStatus, substituteTeacher, substitutePackUser } = d.data;
                let cancelBy = d.service.getSubstituteCancelBy({ result: origin }, d.params);
                if (substituteTeacherStatus == 1 || substituteTeacher) {
                    d.data.substituteOperateAt = new Date();
                }
                if (substituteTeacher && !origin.substituteWithin && ((_c = origin === null || origin === void 0 ? void 0 : origin.substituteServicePackSnapshot) === null || _c === void 0 ? void 0 : _c.isOnCampus)) {
                    // 校外线下抢单成功
                    let isCompensation = await d.service.getIsCompensation({ session: d.id, substituteTeacher });
                    if (!isCompensation) {
                        d.data.substituteNoCompensation = true;
                    }
                }
                if (substitutePackUser) {
                    let packUser = await d.app.service('service-pack-user').Model.findOne({ _id: substitutePackUser }).lean();
                    d.data.substituteServicePackSnapshot = packUser.snapshot;
                    d.data.substituteServicePackUserSnapshot = packUser;
                    // 服务包扣费
                    let serviceResult = await d.service.substituteServiceUse({ doc: origin, packUser }, d.params);
                    if (!serviceResult.success) {
                        return Promise.reject(new Error(serviceResult.message));
                    }
                    d.data.substituteDuration = serviceResult.duration;
                }
                if (cancelBy === 'admin' && !origin.substituteWithin && ((_e = (_d = d.data) === null || _d === void 0 ? void 0 : _d.$unset) === null || _e === void 0 ? void 0 : _e.hasOwnProperty('substituteTeacher'))) {
                    // 更换老师=管理员 校外 取消已匹配老师
                    d.service.substituteSend(origin, 'CancellationOfSubstituteService', d.params);
                }
                if (cancelBy === 'teacher' && !origin.substituteWithin && origin.substituteTeacherStatus == 1 && ((_g = (_f = d.data) === null || _f === void 0 ? void 0 : _f.$unset) === null || _g === void 0 ? void 0 : _g.hasOwnProperty('substituteTeacher'))) {
                    // 代课老师取消
                    d.service.substituteSend(origin, 'TerminationOfSubstituteServiceByServiceProvider(To admin)', d.params);
                    // 2小时内 取消代课 退还服务包时间
                    d.service.handleCancelByTeacher({ doc: origin }, d.params);
                }
                // 校外 取消代课
                if ((cancelBy === 'admin' || cancelBy === 'owner') &&
                    !origin.substituteWithin &&
                    origin.substitutePackUser &&
                    ((_j = (_h = d.data) === null || _h === void 0 ? void 0 : _h.$unset) === null || _j === void 0 ? void 0 : _j.hasOwnProperty('substitutePackUser'))) {
                    d.service.handleServicePackReturn({ doc: origin }, d.params);
                }
                // 开课时间重排 退服务包课时长 取消代课
                if ((_k = d.data) === null || _k === void 0 ? void 0 : _k.start) {
                    if (!origin.substituteWithin && origin.substitutePackUser) {
                        // 校外已预约退服务包
                        await d.service.handleServicePackReturn({ doc: origin }, d.params);
                    }
                    d.data.$unset = Object.assign((_l = d.data.$unset) !== null && _l !== void 0 ? _l : {}, {
                        substituteWithin: '',
                        substituteTeacher: '',
                        substituteTeacherStatus: '',
                        substituteTeacherMessage: '',
                        substituteAdmin: '',
                        substituteAdminStatus: '',
                        substituteAdminMessage: '',
                        substitutePackUser: '',
                        substituteServicePackSnapshot: '',
                        substituteServicePackUserSnapshot: '',
                        substituteSubject: '',
                        substituteTopic: '',
                        substitutePush: '',
                        substituteDuration: '',
                        substitutePushTime: '',
                        substitutePushAll: '',
                        substitutePriorityPush: '',
                        substituteReminder: '',
                        substituteNoCompensation: '',
                        zoom: '',
                    });
                }
                if (origin.substituteTeacherStatus == 1 && ((_o = (_m = d.data) === null || _m === void 0 ? void 0 : _m.$unset) === null || _o === void 0 ? void 0 : _o.hasOwnProperty('substituteTeacher'))) {
                    await d.service.Model.updateOne({ _id: d.id }, { $addToSet: { substituteExclude: origin.substituteTeacher } });
                }
                // zoom
                if (d.data.zoom) {
                    const rs = await d.app.service('zoom-meet').create({ sid: origin.sid, start: origin.start, name: origin.name, ...d.data.zoom }, d.params);
                    if (rs)
                        d.data.zoom = rs;
                    else
                        delete d.data.zoom;
                }
            },
        ],
        remove: [
            authenticate('jwt'),
            (d) => {
                var _a, _b;
                if (hook_1.default.roleHas(['sys'])(d))
                    return d;
                // 老师只能删除自己排的课
                // 学生只能删除自己预约的课 https://github.com/zran-nz/bug/issues/4973
                const uid = (_a = d.params.user) === null || _a === void 0 ? void 0 : _a._id;
                const query = {
                    $or: [{ uid }, { students: uid, booking: { $exists: true } }],
                };
                Object.assign((_b = d.params.query) !== null && _b !== void 0 ? _b : {}, query);
            },
        ],
    },
    after: {
        all: [],
        find: [
            async (d) => {
                var _a, _b;
                if (!((_a = d.result) === null || _a === void 0 ? void 0 : _a.data))
                    return;
                for (const one of d.result.data) {
                    await d.service.ext(one, d.params);
                    await d.service.extService(one, d.params);
                    await d.service.extRating(d.result, d.params);
                }
                await d.service.extBooking(d.result.data, d.params);
                if (isDev) {
                    d.result.query = d.params.query;
                    d.result.ext = { uid: (_b = d.params.user) === null || _b === void 0 ? void 0 : _b._id };
                }
            },
        ],
        get: [
            async (d) => {
                var _a;
                if (hook_1.default.classExist(d))
                    return d;
                if (!((_a = d.result) === null || _a === void 0 ? void 0 : _a._id))
                    return d;
                // await d.service.extTask(d.result)
                // await d.service.extSlides(d.result)
                await d.service.extSnapshot(d.result);
                await d.service.ext(d.result, d.params);
                await d.service.extService(d.result, d.params);
                await d.service.extRating(d.result, d.params);
            },
        ],
        create: [
            async (d) => {
                var _a;
                if (!((_a = d.result) === null || _a === void 0 ? void 0 : _a._id))
                    return;
                const { _id, sid, block, students } = d.result;
                d.service.sendZoomNotice(d.result, d.params);
                // 创建 rooms, 并且处理 block 数据 https://github.com/zran-nz/bug/issues/3238
                const roomsPost = { sid };
                if (block && !Acan.isEmpty(students))
                    roomsPost.block = students;
                await d.app.service('rooms').Model.create(roomsPost);
                if (d.data.childs && _id) {
                    const childs = d.data.childs.map((v) => v._id);
                    await d.service.Model.updateMany({ _id: { $in: childs }, pid: { $exists: false } }, { $set: { pid: _id } });
                }
                // 老师对预约进行排课后，更新 service-booking.session
                if (d.result.booking && _id) {
                    const { name, image, status, booking } = d.result;
                    await d.app.service('service-booking').patch(booking, {
                        session: {
                            _id,
                            name,
                            image,
                            status,
                        },
                    });
                    d.app
                        .service('service-booking')
                        .getTutorialPackId({ _id: booking })
                        .then((tutorialPackUser) => {
                        d.service.Model.updateOne({ _id }, { $set: { tutorialPackUser } }).then();
                    })
                        .catch((e) => {
                        // 找不到辅导包
                        logger_1.default.info(e.message);
                    });
                }
            },
        ],
        update: [],
        patch: [
            async (d) => {
                var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l;
                if (hook_1.default.classExist(d))
                    return d;
                const { pid, _id, sid, booking, premium } = d.result;
                if (booking && d.data.status === 'close') {
                    d.app.service('sales-follow-up').handleFollowUpUnpaid(booking);
                    d.service.sendEndNotice(d.result, d.params);
                }
                // 课堂结束 关闭未付费prompt订单
                if (d.data.status === 'close') {
                    d.app
                        .service('order')
                        .Model.updateMany({ status: 100, links: { $elemMatch: { style: 'prompt', sessionId: _id.toString() } } }, { status: 400 })
                        .exec();
                }
                // 1v1捆绑精品lecture end
                if (d.data.status === 'close') {
                    d.service.sendPremiumNotice(d.result, d.params);
                }
                // substitute
                let origin = (_a = d.params.$origin) !== null && _a !== void 0 ? _a : {};
                let cancelBy = d.service.getSubstituteCancelBy({ result: origin }, d.params);
                let { substituteWithin } = d.result;
                let { substituteTeacher, substituteAdmin, substituteAdminStatus } = d.data;
                // 校内 邀请代课老师
                if (substituteWithin && substituteTeacher) {
                    d.service.substituteSend(d.result, 'InvitedForSubstituteTeaching', d.params);
                }
                // 校内 代课老师拒绝
                if (origin.substituteWithin && cancelBy === 'teacher' && ((_c = (_b = d.data) === null || _b === void 0 ? void 0 : _b.$unset) === null || _c === void 0 ? void 0 : _c.hasOwnProperty('substituteTeacher'))) {
                    d.service.substituteSend(d.result, 'SubstituteInvitationRejected', d.params);
                }
                // 校外 老师邀请管理员
                if (!substituteWithin && substituteAdmin) {
                    d.service.substituteSend(d.result, 'RequestForSubstituteTeacher', d.params);
                }
                // 校外 管理员取消
                if (!origin.substituteWithin && cancelBy === 'admin' && ((_e = (_d = d.data) === null || _d === void 0 ? void 0 : _d.$unset) === null || _e === void 0 ? void 0 : _e.hasOwnProperty('substituteAdmin'))) {
                    if (origin.substituteTeacher) {
                        d.service.substituteSend(origin, 'CancellationOfSubstituteService', d.params);
                    }
                    d.service.substituteSend(origin, 'RejectOfSubstituteTeachingRequest', d.params);
                    d.service.sendCancelNotice({ doc: origin });
                }
                // 校外 发起人取消
                if (!origin.substituteWithin && cancelBy === 'owner' && ((_g = (_f = d.data) === null || _f === void 0 ? void 0 : _f.$unset) === null || _g === void 0 ? void 0 : _g.hasOwnProperty('substituteAdmin'))) {
                    if (origin.substituteTeacher) {
                        d.service.substituteSend(origin, 'CancellationOfSubstituteService', d.params);
                    }
                    if (origin.substituteAdminStatus == 1) {
                        d.service.substituteSend(origin, 'CancellationOfSubstituteService(97)', d.params);
                    }
                    d.service.sendCancelNotice({ doc: origin });
                }
                if (substituteAdminStatus == 1) {
                    d.service.substituteSend(d.result, 'ApprovalOfSubstituteTeachingRequest', d.params);
                }
                // 同步状态到预约数据中，更新 service-booking
                if (booking && d.data.status) {
                    await d.app.service('service-booking').patch(booking, { 'session.status': d.data.status });
                }
                // auto update parent status
                if (pid && d.data.status) {
                    if (d.data.status === 'close') {
                        d.service.endParent(pid);
                        // clean student countdown
                        await d.app.get('redis').DEL(d.app.service('rooms').countDownKey(sid));
                    }
                    else {
                        await d.service.liveParent(d.result, d.params);
                    }
                }
                if (((_h = d.data.$pull) === null || _h === void 0 ? void 0 : _h.reg) || ((_j = d.data.$addToSet) === null || _j === void 0 ? void 0 : _j.reg)) {
                    d.app
                        .service('session')
                        .Model.updateOne({ _id }, { $set: { regNum: ((_k = d.result.reg) === null || _k === void 0 ? void 0 : _k.length) || 0 } })
                        .then();
                }
                if ((_l = d.data.$addToSet) === null || _l === void 0 ? void 0 : _l.reg) {
                    await d.service.regNotice(d.data, d.params, d.result, d.data.$addToSet.reg);
                }
                if (d.data.$pull || d.data.$addToSet) {
                    const rs = { _id: d.id };
                    for (const key in d.data.$pull || d.data.$addToSet) {
                        Acan.objSet(rs, key, Acan.objGet(d.result, key));
                    }
                    d.result = rs;
                }
                else {
                    d.result = { _id: d.id, sid: d.result.sid, ...d.data };
                }
            },
        ],
        remove: [
            async (d) => {
                const { _id, sid, booking, zoom, isAutoCreate, substituteAdmin, substituteAdminStatus, substituteTeacher } = d.result;
                await d.app.service('order').getCancelByLinkId({ linkId: _id.toString(), status: 501 }, {});
                d.service.removeRelated(d.result, d.params).then();
                if (sid)
                    await d.app.service('rooms').Model.deleteOne({ sid });
                d.app.service('zoom-meet').rmBySession(zoom);
                d.service.removeNotice(d.result, d.params);
                if (isAutoCreate && booking) {
                    // 自动排课的需要同时删除预订
                    await d.app.service('service-booking').remove(booking, d.params);
                }
                else if (booking && _id) {
                    // 老师取消课程后，移除 service-booking.session
                    await d.app.service('service-booking').unBindSession(booking, d.params);
                }
                // premium_cloud认证精品课快照,订单使用状态更新
                d.app.service('order').Model.updateOne({ 'links.session': _id.toString() }, { 'links.$.used': false }).exec();
                // new prompt订单关闭
                d.app
                    .service('order')
                    .Model.updateMany({ status: 100, links: { $elemMatch: { style: 'prompt', sessionId: _id.toString() } } }, { status: 400 })
                    .exec();
                // todo refund
                // substitute
                if (substituteAdminStatus == 1) {
                    d.service.substituteSend(d.result, 'CancellationOfSubstituteService(97)', d.params);
                }
                if (substituteTeacher) {
                    d.service.substituteSend(d.result, 'CancellationOfSubstituteService', d.params);
                }
                if (!d.result.substituteWithin && d.result.substitutePackUser) {
                    d.service.handleServicePackReturn({ doc: d.result }, d.params);
                }
            },
        ],
    },
    error: {
        all: [
            (d) => {
                if (d.error.code === 404)
                    return d;
                logger_1.default.error(d.error);
            },
        ],
        find: [],
        get: [],
        create: [],
        update: [],
        patch: [],
        remove: [],
    },
};
