"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const service_fans_class_1 = require("./service-fans.class");
const service_fans_model_1 = __importDefault(require("../../models/service-fans.model"));
const service_fans_hooks_1 = __importDefault(require("./service-fans.hooks"));
function default_1(app) {
    const options = {
        Model: (0, service_fans_model_1.default)(app),
        paginate: app.get('paginate')
    };
    // Initialize our service with any options it requires
    app.use('/service-fans', new service_fans_class_1.ServiceFans(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('service-fans');
    service.hooks(service_fans_hooks_1.default);
}
exports.default = default_1;
