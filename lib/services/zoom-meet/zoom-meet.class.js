"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ZoomMeet = void 0;
const logger_1 = __importDefault(require("../../logger"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const got = require('got');
class ZoomMeet {
    constructor(options = {}, app) {
        this.options = options;
        this.app = app;
    }
    async rmBySession(zoom) {
        if (!(zoom === null || zoom === void 0 ? void 0 : zoom.id))
            return;
        await this.app
            .service('zoom-meet')
            .remove(zoom.id, { zoom: zoom.host_id })
            .catch((err) => {
            logger_1.default.warn('zoom remove error:', err);
        });
    }
    // async oldData (params?: Params): Promise<any> {
    //   const { _id, uid } = params?.user ?? {}
    //   const [rs] = await knexj('sys_third_account').where({ third_type: 'zoom', sys_user_id: uid || _id }).limit(1)
    //   if (!rs) return null
    //   const sub = rs.third_user_uuid
    //   let { access_token: token, refresh_token: rt } = rs
    //   const con = { type: 'zoom', sub }
    //   const post = { token, rt, email: rs.email, ext: JSON.parse(rs.third_ext_data) }
    //   this.app.service('users').Model.updateOne({ _id }, { zoom: sub })
    //   return { ...con, ...post }
    // }
    async getAuth(params) {
        var _a;
        const zoom = (params === null || params === void 0 ? void 0 : params.zoom) || ((_a = params === null || params === void 0 ? void 0 : params.user) === null || _a === void 0 ? void 0 : _a.zoom) || null;
        if (!zoom)
            return logger_1.default.warn('no bind zoom'), null;
        let rs = await this.app.service('user-token').Model.findOne({ type: 'zoom', sub: zoom });
        if (!rs)
            return logger_1.default.warn('zoom token empty'), null;
        let payload = jsonwebtoken_1.default.decode(rs.token);
        if (Date.now() / 1000 > payload.exp) {
            rs = await this.app.service('user-token').refreshToken(rs);
            if (!rs)
                return null;
        }
        return rs;
    }
    async getHeaders(params) {
        const auth = await this.getAuth(params);
        if (!auth)
            return {};
        return { Authorization: 'Bearer ' + auth.token };
    }
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    async find(params) {
        return (await got(`https://api.zoom.us/v2/users/me/meetings`, { headers: await this.getHeaders(params), json: true })).body;
    }
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    async get(id, params) {
        var _a;
        const { zoom } = (_a = params === null || params === void 0 ? void 0 : params.user) !== null && _a !== void 0 ? _a : {};
        const getMod = {
            users: async () => {
                return (await got(`https://api.zoom.us/v2/users`, { headers: await this.getHeaders(params), json: true })).body;
            },
            check: async () => {
                if (!zoom)
                    return false;
                let rs = Acan.clone(await this.app.service('user-token').Model.findOne({ type: 'zoom', sub: zoom }));
                if (!rs)
                    return false;
                if (Date.now() / 1000 > rs.exp) {
                    const { exp } = (await this.app.service('user-token').refreshToken(rs)) || {};
                    if (!exp)
                        return false;
                    rs.exp = exp;
                }
                delete rs.rt;
                delete rs.token;
                return rs;
            },
            rt: async () => {
                let rs = await this.app.service('user-token').Model.findOne({ type: 'zoom', sub: zoom });
                return await this.app.service('user-token').refreshToken(rs);
            },
            unbind: async () => {
                if (!zoom)
                    return { message: 'no bind zoom' };
                const doc = await this.app.service('user-token').Model.findOne({ type: 'zoom', sub: zoom });
                if (!doc)
                    return {};
                return await this.app.service('user-token').remove(doc._id);
            },
        };
        if (getMod[id])
            return getMod[id]();
        return {
            id,
            text: `A new message with ID: ${id}!`,
        };
    }
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    async create(data, params) {
        var _a;
        const auth = await this.getAuth(params);
        if (!(auth === null || auth === void 0 ? void 0 : auth.token))
            return logger_1.default.warn('zoom auth error:', auth, params === null || params === void 0 ? void 0 : params.user), null;
        const headers = { Authorization: 'Bearer ' + auth.token, Accept: 'application/json, text/plain, */*', 'Content-Type': 'application/json' };
        const post = {
            agenda: data.name,
            default_password: false,
            duration: 60,
            password: data.sid || '123456',
            pre_schedule: false,
            schedule_for: auth.email,
            settings: { join_before_host: !data.waiting_room, waiting_room: !!data.waiting_room },
            start_time: data.start,
            type: 2,
        };
        const rs = await got
            .post(`https://api.zoom.us/v2/users/me/meetings`, {
            headers,
            body: JSON.stringify(post),
            json: true,
        })
            .catch((err) => {
            logger_1.default.warn(err);
            return { code: err.statusCode, err };
        });
        if ((_a = rs === null || rs === void 0 ? void 0 : rs.body) === null || _a === void 0 ? void 0 : _a.id) {
            for (const key of ['encrypted_password', 'h323_password', 'is_simulive', 'pstn_password', 'settings'])
                delete rs.body[key];
            return rs.body;
        }
        else {
            this.app.service('log').create({
                type: 'node.zoom-meet.create',
                ip: (params === null || params === void 0 ? void 0 : params.Aip) || global.LocalIp,
                status: rs.code,
                stack: JSON.stringify(rs),
                body: { post, data },
                msg: rs.message,
            });
            return rs;
        }
    }
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    async update(id, data, params) {
        return data;
    }
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    async patch(id, data, params) {
        return data;
    }
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    async remove(id, params) {
        const headers = await this.getHeaders(params);
        return (await got.delete(`https://api.zoom.us/v2/meetings/${id}`, { headers })).body;
    }
}
exports.ZoomMeet = ZoomMeet;
