"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const got = require('got');
const logger_1 = __importDefault(require("../../logger"));
const mod = {
    async checkUrl(d) {
        var _a, _b;
        const url = (_b = (_a = d.params.query) === null || _a === void 0 ? void 0 : _a.url) === null || _b === void 0 ? void 0 : _b.trim();
        if (!url)
            return (d.result = false), d;
        d.result = (await got.head(url, { json: true })).body;
    },
    async test(d) {
        var _a, _b;
        const { roles } = (_a = d.params.user) !== null && _a !== void 0 ? _a : {};
        if (!roles.includes('sys'))
            return (d.result = { message: 'no power' }), d;
        const { model, method, args } = (_b = d.params.query) !== null && _b !== void 0 ? _b : {};
        d.result = await d.app.service(model)[method](...args);
    },
    async redisKeys(d) {
        var _a;
        const { keys } = (_a = d.params.query) !== null && _a !== void 0 ? _a : {};
        const redis = d.app.get('redis');
        logger_1.default.info(keys);
        const rs = await redis.keys(keys);
        d.result = rs;
    },
    async redis(d) {
        var _a;
        const { act, args } = (_a = d.params.query) !== null && _a !== void 0 ? _a : {};
        const redis = d.app.get('redis');
        logger_1.default.info(act, args);
        const rs = await redis[act](...args);
        logger_1.default.info(rs);
        d.result = rs;
    },
    async upUsers(d) {
        // const rs = await d.app.service('users').Model.find({}).select(['email']).limit(2000)
        // const list = []
        // for (const one of rs) {
        //   if (!one._id) continue
        //   if (one.email !== one.email.toLowerCase()) {
        //     const nrs = await d.app.service('users').Model.updateOne({ _id: one._id }, { email: one.email.toLowerCase() })
        //     list.push({ ...one, ...nrs })
        //   }
        // }
        // d.result = { list, total: rs.length, one: rs[0] }
    },
    async syncTest(d) { },
    async syncDb(d) {
        var _a;
        const { model, run } = (_a = d.params.query) !== null && _a !== void 0 ? _a : {};
        if (!['sessions', 'collects', 'confusers', 'outlines', 'materials', 'promotes', 'responses', 'reviews', 'usercerts'].includes(model)) {
            d.result = { message: 'error model', model };
            return;
        }
        const client = d.app.get('mongoClient');
        const db = client.collection(model);
        const ids = await db.distinct('uid', { $or: [{ uid: /^\d+$/ }, { uid: { $lt: 1000000 } }] });
        const rs = await d.app
            .service('users')
            .Model.find({ id: { $in: ids } })
            .select('id');
        const list = {};
        rs.map((v) => {
            list[v.id] = v._id;
        });
        const upRs = [];
        if (run) {
            for (const id of ids) {
                if (list[id]) {
                    upRs.push(await db.updateMany({ uid: id }, { $set: { uid: list[id] } }));
                }
            }
        }
        d.result = { model, run, ids, list, upRs };
    },
    // async syncUser(d: HookContext) {
    //   const {limit = 5, skip = 0} = d.params.query || {}
    //   const rs = await knex('users')
    //     .whereNotNull('uid')
    //     .select('id', 'uid', 'nickname', 'avatar', 'email', 'user_name as username', 'roles', 'created_at as createdAt', 'updated_at as updatedAt')
    //     .offset(skip)
    //     .limit(limit)
    //   rs.map((v: any) => {
    //     if (!v.roles) delete v.roles
    //     d.app.service('users').Model.updateOne({uid: v.uid}, v, {upsert: true}).then()
    //   })
    //   d.result = {count: rs.length, limit, skip}
    // },
    // async taskStats(d: HookContext) {
    //   const {task} = d.params.query ?? {}
    //   if (!task) return (d.result = null), d
    //   let [rs] = await knexj('view_content').where({id: task}).select('sid', 'rev', 'type', 'updatedAt').limit(1)
    //   if (!rs) return (d.result = null), d
    //   const slides = await d.app.service('slides').Model.find({id: rs.sid}).select(['id', 'rev', 'task', 'pages._id', 'updatedAt'])
    //   let old
    //   if (rs.type === 4)
    //     old = await knexj('cc_presentation_images')
    //       .where({presentation_id: rs.sid, revision_id: rs.rev})
    //       .orderBy('page_num', 'asc')
    //       .select('page_num as id', 'presentation_id as sid', 'revision_id as rev', 'update_time as updatedAt', 'page_id as page')
    //       .limit(200)
    //   else
    //     old = await knex('slide_page_thumbnails')
    //       .where({presentation_id: rs.sid, revision_id: rs.rev})
    //       .orderBy('id', 'asc')
    //       .select('id', 'presentation_id as sid', 'revision_id as rev', 'updated_at as updatedAt', 'page_id as page')
    //       .limit(200)
    //   d.result = {task: rs, slides, old}
    // },
    async upSlides(d) {
        var _a;
        const { limit = 1, del } = (_a = d.params.query) !== null && _a !== void 0 ? _a : {};
        const model = d.app.service('slides').Model;
        const rs = await model.aggregate([{ $group: { _id: '$id', count: { $sum: 1 } } }, { $sort: { count: -1 } }, { $limit: parseInt(limit) }]);
        const arr = [rs];
        if (del) {
            for (const one of rs) {
                const docs = await model.find({ id: one._id }).select(['rev', 'task', 'updatedAt']).sort({ updatedAt: -1 });
                for (const doc of docs.slice(1)) {
                    await model.deleteOne({ _id: doc._id });
                }
                arr.push(docs);
            }
        }
        d.result = arr;
    },
};
exports.default = mod;
