"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const sales_follow_up_class_1 = require("./sales-follow-up.class");
const sales_follow_up_model_1 = __importDefault(require("../../models/sales-follow-up.model"));
const sales_follow_up_hooks_1 = __importDefault(require("./sales-follow-up.hooks"));
function default_1(app) {
    const options = {
        Model: (0, sales_follow_up_model_1.default)(app),
        paginate: app.get('paginate'),
        whitelist: ['$regex', '$options', '$search'],
    };
    // Initialize our service with any options it requires
    app.use('/sales-follow-up', new sales_follow_up_class_1.SalesFollowUp(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('sales-follow-up');
    service.hooks(sales_follow_up_hooks_1.default);
}
exports.default = default_1;
