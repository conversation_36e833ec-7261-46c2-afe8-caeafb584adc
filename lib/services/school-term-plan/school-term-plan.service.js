"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const school_term_plan_class_1 = require("./school-term-plan.class");
const school_term_plan_model_1 = __importDefault(require("../../models/school-term-plan.model"));
const school_term_plan_hooks_1 = __importDefault(require("./school-term-plan.hooks"));
function default_1(app) {
    const options = {
        Model: (0, school_term_plan_model_1.default)(app),
        paginate: app.get('paginate')
    };
    // Initialize our service with any options it requires
    app.use('/school-term-plan', new school_term_plan_class_1.SchoolTermPlan(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('school-term-plan');
    service.hooks(school_term_plan_hooks_1.default);
}
exports.default = default_1;
