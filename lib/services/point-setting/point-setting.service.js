"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const point_setting_class_1 = require("./point-setting.class");
const point_setting_model_1 = __importDefault(require("../../models/point-setting.model"));
const point_setting_hooks_1 = __importDefault(require("./point-setting.hooks"));
function default_1(app) {
    const options = {
        Model: (0, point_setting_model_1.default)(app),
        paginate: app.get('paginate'),
    };
    // Initialize our service with any options it requires
    app.use('/point-setting', new point_setting_class_1.PointSetting(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('point-setting');
    service.hooks(point_setting_hooks_1.default);
}
exports.default = default_1;
