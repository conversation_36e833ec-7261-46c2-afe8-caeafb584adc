"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ShareInfo = void 0;
const feathers_mongoose_1 = require("feathers-mongoose");
const { NotFound, GeneralError, BadRequest } = require('@feathersjs/errors');
class ShareInfo extends feathers_mongoose_1.Service {
    //eslint-disable-next-line @typescript-eslint/no-unused-vars
    constructor(options, app) {
        super(options);
        this.app = app;
    }
}
exports.ShareInfo = ShareInfo;
