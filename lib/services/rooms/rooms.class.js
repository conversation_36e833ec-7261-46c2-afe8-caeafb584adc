"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Rooms = void 0;
const feathers_mongoose_1 = require("feathers-mongoose");
const logger_1 = __importDefault(require("../../logger"));
class Rooms extends feathers_mongoose_1.Service {
    //eslint-disable-next-line @typescript-eslint/no-unused-vars
    constructor(options, app) {
        super(options);
        this.app = app;
    }
    // 通过课堂短id查询room
    async bySid(sid, params) {
        let rs = await this.Model.findOne({ sid });
        if (!rs)
            rs = await this.Model.create({ sid });
        return rs;
    }
    memberAddFn(user) {
        const member = { last: new Date() };
        if (!user.nickname && !Acan.isEmpty(user.name))
            user.nickname = user.name.join(' ');
        for (const key of ['_id', 'nickname', 'avatar', 'email']) {
            if (user[key])
                member[key] = user[key];
        }
        return member;
    }
    // 学生加入课堂，课堂开始时间到了才能加入
    async joinRoom(_id, user, isTest = false) {
        var _a;
        const rs = await this.Model.findById(_id).select(['sid', 'members', 'attend', 'block']);
        if (!rs)
            return { message: 'rooms not found' };
        const sessionSelect = ['sid', 'pid', 'block', 'students', 'reg', 'guest', 'school'];
        // 获取session
        let sdoc = await this.app.service('session').Model.findOne({ sid: rs.sid }).select(sessionSelect);
        if (!sdoc)
            return { message: 'session not found' };
        // 读取学校的用户信息
        if (sdoc.school && user.email) {
            let userInfo;
            if ((_a = user.roles) === null || _a === void 0 ? void 0 : _a.includes('student')) {
                userInfo = await this.app.service('students').getInfo({ school: sdoc.school, email: user.email });
            }
            else {
                userInfo = await this.app.service('school-user').getInfo({ school: sdoc.school, email: user.email });
            }
            if (userInfo)
                user = { ...user, ...userInfo, _id: user._id };
        }
        const isBlock = sdoc.block;
        const isGuest = sdoc.guest;
        // 如果是子课堂，继续获取父级的session
        if (sdoc.pid)
            sdoc = await this.app.service('session').Model.findById(sdoc.pid).select(sessionSelect);
        if (sdoc.pid)
            sdoc = await this.app.service('session').Model.findById(sdoc.pid).select(sessionSelect);
        const { students, reg } = sdoc;
        const $addToSet = {};
        // 检查是否已经进入过课堂
        let members = rs.members.find((v) => v._id.toString() === user._id);
        if (!members) {
            $addToSet.members = this.memberAddFn(user);
            // 获取报名或排课的学生数据
            const mrr = !Acan.isEmpty(students) ? students : reg.map((v) => v._id);
            if (!rs.attend.includes(user._id))
                $addToSet.attend = user._id;
            if (!mrr.includes(user._id) && !isGuest)
                ($addToSet.block = user._id), logger_1.default.warn('rooms block by guest'); // guest to block, no anonymous
        }
        if (isBlock)
            ($addToSet.block = user._id), logger_1.default.warn('rooms block by session block'); // session.block === true, need block everyone
        // has members
        if (Acan.isEmpty($addToSet))
            return logger_1.default.info('has rooms members', rs.sid, user.nickname), null;
        if (!isTest)
            await this.Model.updateOne({ _id }, { $addToSet });
        logger_1.default.warn('joinRoom:', user.nickname, user._id, $addToSet);
        const result = { _id, sid: rs.sid, ...$addToSet };
        result.attend = [...rs.attend];
        result.block = [...rs.block];
        if ($addToSet.attend && !result.block.includes($addToSet.attend))
            result.attend.push($addToSet.attend);
        if ($addToSet.block && !result.block.includes($addToSet.block))
            result.block.push($addToSet.block);
        return result;
    }
    async getJoinRoomTest({ _id, uid }, params) {
        const user = await this.app.service('users').uidToInfo(uid);
        return await this.joinRoom(_id, user, true);
    }
    patchJoinRoom(data, params) {
        var _a;
        // students join room only once
        const { _id } = data || params.query || {};
        const user = Acan.clone((_a = params.user) !== null && _a !== void 0 ? _a : {});
        return this.joinRoom(_id, user);
    }
    // 老师加入课堂，任何时候都能加入
    async patchJoinRoomTeacher(data, params) {
        var _a;
        const { _id } = data || params.query || {};
        const user = Acan.clone((_a = params.user) !== null && _a !== void 0 ? _a : {});
        const rs = await this.Model.findById(_id).select(['sid', 'teachers']);
        if (!rs)
            return { message: 'rooms not found' };
        const $addToSet = {};
        // 检查是否已经进入过课堂
        let teachers = rs.teachers.find((v) => v._id.toString() === user._id);
        if (!teachers) {
            $addToSet.teachers = this.memberAddFn(user);
        }
        if (!Acan.isEmpty($addToSet))
            await this.Model.updateOne({ _id }, { $addToSet });
        logger_1.default.warn('joinRoomTeacher:', user.nickname, user._id, $addToSet);
        return { _id, sid: rs.sid, ...$addToSet };
    }
    countDownKey(sid) {
        return `countdown:${sid}`;
    }
    async getCountdownStart({ sid }, params) {
        var _a;
        const uid = (_a = params.user) === null || _a === void 0 ? void 0 : _a._id;
        const redis = this.app.get('redis');
        let start = await redis.HGET(this.countDownKey(sid), uid);
        return start || null;
    }
    async patchCountdownStart({ sid }, params) {
        var _a;
        const uid = (_a = params.user) === null || _a === void 0 ? void 0 : _a._id;
        const redis = this.app.get('redis');
        let start = await redis.HGET(this.countDownKey(sid), uid);
        if (!start) {
            start = new Date().toISOString();
            await redis.HSET(this.countDownKey(sid), uid, start);
        }
        return start;
    }
    async getCountdownList({ sid }, params) {
        const redis = this.app.get('redis');
        return await redis.HGETALL(this.countDownKey(sid));
    }
    // App.service('rooms').get('countStatus', {query: {sid: {$in: [sid, ...]}}})
    async getCountStatus({ sid }, params) {
        const list = await this.Model.find({ sid });
        const rs = {};
        list.map((v) => {
            rs[v.sid] = { members: v.members.length, groups: v.groups.length, attend: v.attend.length, block: v.block.length };
        });
        return rs;
    }
}
exports.Rooms = Rooms;
