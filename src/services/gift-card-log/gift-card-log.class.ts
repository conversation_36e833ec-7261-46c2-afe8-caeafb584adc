import {Service, MongooseServiceOptions} from 'feathers-mongoose'
import {Application} from '../../declarations'
import {BadRequest, NotFound} from '@feathersjs/errors'

export class GiftCardLog extends Service {
  app: Application

  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.app = app
  }

  async createGiftCardLog(data: any, params?: any): Promise<any> {
    const {uid, tab, source, category, value, businessId, snapshot, isSchool, giftCardId} = data

    if (!uid || !tab || !source || !category || value === undefined) {
      throw new BadRequest('Missing required fields')
    }

    const numericValue = Number(value)

    const modelName = isSchool ? 'school-plan' : 'users'
    const query =
      numericValue < 0
        ? {
            _id: uid,
            $expr: {$gte: [{$add: [{$ifNull: ['$giftCardBalance', 0]}, numericValue]}, 0]},
          }
        : {_id: uid}

    const options = Acan.getTxnOptions(params)
    const updateOptions: any = {
      new: true,
      upsert: false,
      ...options,
    }

    // Update user/school balance
    const updatedAccount = (await this.app.service(modelName).Model.findOneAndUpdate(query, {$inc: {giftCardBalance: numericValue}}, updateOptions)) as any

    if (!updatedAccount) {
      const account = isSchool
        ? await this.app.service('school-plan').Model.findOne({_id: uid}, null, options)
        : await this.app.service('users').Model.findOne({_id: uid}, null, options)

      if (!account) {
        throw new NotFound('User or school not found')
      } else {
        throw new BadRequest('Insufficient gift card balance')
      }
    }

    // Options for transaction record creation
    const transactionOptions: any = {
      upsert: true,
      new: true,
      setDefaultsOnInsert: true,
      ...options,
    }

    // Create transaction record with the updated balance
    const transaction = await this.Model.findOneAndUpdate(
      {
        uid,
        tab,
        source,
        category,
        businessId,
        isSchool: isSchool || false,
        giftCardId: giftCardId || null,
      },
      {
        $set: {
          uid,
          tab,
          source,
          category,
          value: numericValue,
          total: updatedAccount.giftCardBalance,
          businessId,
          snapshot,
          isSchool: isSchool || false,
          giftCardId,
          status: 1,
        },
      },
      transactionOptions
    )

    return transaction
  }
}
