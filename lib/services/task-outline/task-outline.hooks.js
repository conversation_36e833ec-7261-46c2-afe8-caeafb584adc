"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const authentication = __importStar(require("@feathersjs/authentication"));
// Don't remove this comment. It's needed to format import lines nicely.
const { authenticate } = authentication.hooks;
const task_outline_mod_1 = __importDefault(require("./task-outline.mod"));
const logger_1 = __importDefault(require("../../logger"));
const bson_1 = require("bson");
const hook_1 = __importDefault(require("../../hook"));
exports.default = {
    before: {
        all: [],
        find: [
            authenticate('jwt'),
            (d) => {
                var _a;
                if ((_a = d.params.query) === null || _a === void 0 ? void 0 : _a.$dev) {
                    d.params.dev = 1;
                    delete d.params.query.$dev;
                }
            },
        ],
        get: [
            hook_1.default.toClass,
            async (d) => {
                logger_1.default.warn(d.id, 'task-outline');
                if (hook_1.default.classExist(d))
                    return d;
                if (d.id && task_outline_mod_1.default[d.id])
                    return task_outline_mod_1.default[d.id](d);
                else if (/^\d+$/.test(d.id + '')) {
                    d.result = await d.service.Model.findOne({ task: d.id });
                }
                else
                    return d;
            },
        ],
        create: [authenticate('jwt')],
        update: [authenticate('jwt')],
        patch: [
            authenticate('jwt'),
            (d) => {
                for (const key of Object.keys(d.data)) {
                    // auto parse unique subjects
                    const krr = key.split('.');
                    const lastKey = krr.pop();
                    if (lastKey === 'data') {
                        const srr = [];
                        Object.keys(d.data[key]).map((code) => {
                            const subject = code.split(':')[1];
                            if (srr.includes(subject))
                                return;
                            srr.push(subject);
                        });
                        d.data[key.replace('.data', '.subjects')] = srr;
                    }
                    else if (lastKey === 'custom') {
                        d.data[key].map((v) => {
                            if (!v._id)
                                v._id = new bson_1.ObjectID().toString();
                        });
                    }
                }
            },
        ],
        remove: [authenticate('jwt')],
    },
    after: {
        all: [],
        find: [],
        get: [],
        create: [
            async (d) => {
                // pd add subject to outline
                const { task } = d.result;
                const $set = {};
                await d.service.unitServiceSet(d.data, $set);
                if (!Acan.isEmpty($set))
                    d.app.service('unit').Model.updateOne({ _id: task }, { $set }).then();
            },
        ],
        update: [],
        patch: [
            async (d) => {
                var _a, _b;
                // related patch to unit.outlineSubjects
                let needUp = false;
                for (const key of Object.keys(d.data)) {
                    if (key.split('.').pop() === 'data')
                        needUp = true;
                }
                if (!needUp)
                    return;
                const { task } = d.result;
                const $set = {};
                const outlineSubjects = [];
                for (const type of ['assess', 'outline', 'pd']) {
                    if (!((_a = d.result[type]) === null || _a === void 0 ? void 0 : _a.data))
                        continue;
                    for (const code in d.result[type].data) {
                        const { name } = (_b = d.result[type].data[code]) !== null && _b !== void 0 ? _b : {};
                        if (!name)
                            continue;
                        if (outlineSubjects.includes(name))
                            continue;
                        outlineSubjects.push(name);
                    }
                }
                if (!Acan.isEmpty(outlineSubjects))
                    $set.outlineSubjects = outlineSubjects;
                // pd add subject to outline
                await d.service.unitServiceSet(d.data, $set);
                if (!Acan.isEmpty($set))
                    d.app.service('unit').Model.updateOne({ _id: task }, { $set }).then();
            },
        ],
        remove: [],
    },
    error: {
        all: [],
        find: [],
        get: [],
        create: [],
        update: [],
        patch: [],
        remove: [],
    },
};
