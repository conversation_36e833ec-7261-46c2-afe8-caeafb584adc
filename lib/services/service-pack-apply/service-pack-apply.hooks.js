"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const authentication = __importStar(require("@feathersjs/authentication"));
const { NotFound, GeneralError, BadRequest } = require('@feathersjs/errors');
// Don't remove this comment. It's needed to format import lines nicely.
const hook_1 = __importDefault(require("../../hook"));
const { authenticate } = authentication.hooks;
const search = require('feathers-mongodb-fuzzy-search');
exports.default = {
    before: {
        all: [authenticate('jwt')],
        find: [
            search({
                fields: ['nickname'],
            }),
        ],
        get: [hook_1.default.toClass],
        create: [
            async (d) => {
                let { uid, sharedSchool, name, servicePack } = d.data;
                d.data.needOrder = true;
                d.data.withinSchool = false;
                if (sharedSchool) {
                    let servicePrice = await d.app.service('service-pack-school-price').Model.findOne({ school: sharedSchool, servicePack });
                    // 报名截止判断
                    let now = new Date().getTime();
                    if (new Date(servicePrice.deadline).getTime() < now) {
                        throw new BadRequest('Apply deadline has passed.');
                    }
                    // 判断是否需要购买
                    if (!servicePrice.priceEnable) {
                        d.data.needOrder = false;
                    }
                    else {
                        d.data.contentOrientated = servicePrice.contentOrientated;
                    }
                    // 判断校内/校外
                    let student = await d.app.service('students').Model.findOne({ uid, school: sharedSchool, servicePack });
                    if (student) {
                        d.data.withinSchool = true;
                    }
                }
                let servicePackData = await d.app.service('service-pack').Model.findOne({ _id: servicePack });
                if (servicePackData === null || servicePackData === void 0 ? void 0 : servicePackData.interviewPack) {
                    d.data.interviewPack = servicePackData.interviewPack._id;
                }
                if (name)
                    d.data.nickname = name.join(' ');
            },
        ],
        update: [],
        patch: [
            async (d) => {
                let { status, interviewInvited } = d.data;
                if (status == 1) {
                    d.data.purchaseExpireAt = Date.now() + 7 * 24 * 3600 * 1000;
                    d.data.approvedAt = Date.now();
                }
                if (interviewInvited) {
                    d.data.interviewPurchaseExpireAt = Date.now() + 7 * 24 * 3600 * 1000;
                    d.data.interviewPurchaseExpired = false;
                }
            },
        ],
        remove: [],
    },
    after: {
        all: [],
        find: [
            async (d) => {
                if (d.result) {
                    for (let i = 0; i < d.result.data.length; i++) {
                        await d.service.extServicePack(d.result.data[i]);
                        await d.service.extUser(d.result.data[i]);
                        await d.service.extSchool(d.result.data[i]);
                        await d.service.extSchoolPrice(d.result.data[i]);
                    }
                }
            },
        ],
        get: [
            async (d) => {
                if (d.result.uid) {
                    await d.service.extServicePack(d.result);
                    await d.service.extUser(d.result);
                    await d.service.extSchool(d.result);
                }
            },
        ],
        create: [
            async (d) => {
                d.service.handleEmail(d.result, d.data, d.params);
            },
        ],
        update: [],
        patch: [
            async (d) => {
                let { status } = d.data;
                let { _id, takeaway } = d.result;
                if (status == -1 && takeaway) {
                    d.service.getInterviewReturn({ id: _id.toString() });
                }
                d.service.handleEmail(d.result, d.data, d.params);
            },
        ],
        remove: [],
    },
    error: {
        all: [],
        find: [],
        get: [],
        create: [],
        update: [],
        patch: [],
        remove: [],
    },
};
