"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const authentication = __importStar(require("@feathersjs/authentication"));
const logger_1 = __importDefault(require("../../logger"));
const bson_1 = require("bson");
const hook_1 = __importDefault(require("../../hook"));
// Don't remove this comment. It's needed to format import lines nicely.
const { authenticate } = authentication.hooks;
exports.default = {
    before: {
        all: [authenticate('jwt')],
        find: [],
        get: [
            hook_1.default.toClass,
            async (d) => {
                if (hook_1.default.classExist(d))
                    return d;
                if ((d.id + '').length < 20)
                    d.result = await d.service.bySid(d.id, d.params);
                return d;
            },
        ],
        create: [],
        update: [],
        patch: [
            hook_1.default.toClass,
            (d) => {
                var _a;
                if ((_a = d.data.$addToSet) === null || _a === void 0 ? void 0 : _a.groups)
                    d.data.$addToSet.groups._id = new bson_1.ObjectID();
            },
        ],
        remove: [],
    },
    after: {
        all: [],
        find: [],
        get: [
            (d) => {
                var _a;
                if (hook_1.default.classExist(d))
                    return d;
                if (!d.result._id)
                    return;
                const { _id } = (_a = d.params.user) !== null && _a !== void 0 ? _a : {};
                const { sid } = d.result;
                if (_id && d.id === sid) {
                    d.app.get('redis').HSET('SessionLastSid', _id, sid);
                    logger_1.default.warn('session last', d.id, _id);
                }
            },
        ],
        create: [],
        update: [],
        patch: [
            (d) => {
                var _a;
                if (hook_1.default.classExist(d))
                    return d;
                if (!((_a = d.result) === null || _a === void 0 ? void 0 : _a.sid))
                    return logger_1.default.warn(d.id, d.data, 'no sid'), d;
                let rs = { _id: d.id, sid: d.result.sid };
                if (d.data.$addToSet)
                    rs = { ...rs, ...d.data.$addToSet };
                if (d.data.$pull) {
                    // 删除操作，只留下 _id
                    for (const key of Object.keys(d.data.$pull)) {
                        rs[key] = { _id: d.data.$pull[key]._id };
                    }
                }
                if (Acan.isDefined(d.data['members.$.group'])) {
                    const id = d.params.query['members._id'];
                    const members = id ? d.result.members.find((v) => v._id === id) : {};
                    rs = { ...rs, members };
                }
                if (Acan.isDefined(d.data['groups.$.name'])) {
                    const id = d.params.query['groups._id'];
                    const groups = id ? d.result.groups.find((v) => v._id.toString() === id) : {};
                    rs = { ...rs, groups };
                }
                if (d.data.groupMax)
                    rs.groupMax = d.data.groupMax;
                if (rs.block)
                    rs.block = d.result.block;
                if (rs.attend)
                    rs.attend = d.result.attend;
                d.result = rs;
            },
        ],
        remove: [],
    },
    error: {
        all: [],
        find: [],
        get: [],
        create: [],
        update: [],
        patch: [],
        remove: [],
    },
};
