"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const session_class_1 = require("./session.class");
const session_model_1 = __importDefault(require("../../models/session.model"));
const session_hooks_1 = __importDefault(require("./session.hooks"));
function default_1(app) {
    const options = {
        Model: (0, session_model_1.default)(app),
        whitelist: ['$exists', '$regex', '$options', '$search'],
        // multi: ['remove'],
        paginate: app.get('paginate')
    };
    // Initialize our service with any options it requires
    app.use('/session', new session_class_1.Session(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('session');
    service.hooks(session_hooks_1.default);
}
exports.default = default_1;
