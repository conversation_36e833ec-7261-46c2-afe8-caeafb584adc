import {GeneralError} from '@feathersjs/errors'
import {HookContext, Params} from '@feathersjs/feathers'
import {ClientSession} from 'mongoose'

export interface TxnParams extends Params {
  mongoose?: {
    session: ClientSession
  }
  sideEffectsToExecute?: SideEffectTask[]
}

// HOOK-BASED TRANSACTION FUNCTIONS

// Before hook - Start transaction
export const startTransaction = () => {
  return async (context: HookContext) => {
    const {app, params} = context

    // Get Mongoose client from your app setup
    const mongooseClient = app.get('mongooseClient')
    if (!mongooseClient) {
      throw new Error('Mongoose client not found')
    }

    // Start session and transaction
    const session = await mongooseClient.startSession()
    session.startTransaction()

    params.mongoose = params.mongoose || {}
    params.mongoose.session = session
    params.sideEffectsToExecute = []

    return context
  }
}

// After hook - Commit transaction
export const commitTransaction = () => {
  return async (context: HookContext) => {
    const mongooseSession = context.params?.mongoose?.session

    if (mongooseSession) {
      try {
        await mongooseSession.commitTransaction()
        executeSideEffects(context.app, context.params)
      } catch (error) {
        throw new GeneralError('Transaction commit failed')
      } finally {
        await endTrxnSession(mongooseSession)
      }
    }

    return context
  }
}

// Error hook - Rollback transaction
export const rollbackTransaction = () => {
  return async (context: HookContext) => {
    const mongooseSession = context.params?.mongoose?.session

    if (mongooseSession) {
      try {
        await mongooseSession.abortTransaction()
      } catch (error) {
      } finally {
        await endTrxnSession(mongooseSession)
      }
    }

    return context
  }
}

// STANDALONE TRANSACTION FUNCTIONS

export interface TransactionResult<T> {
  success: boolean
  data?: T
  error?: Error
}

// Create a new transaction session
export const startTransactionSession = async (app: any): Promise<ClientSession> => {
  const mongooseClient = app.get('mongooseClient')
  const session = await mongooseClient.startSession()
  session.startTransaction()
  return session
}

// Commit a transaction session
export const commitTransactionSession = async (session: ClientSession, app: any, params: Params): Promise<void> => {
  try {
    await session.commitTransaction()
    executeSideEffects(app, params)
  } catch (error) {
    throw new Error('Transaction commit failed')
  } finally {
    await endTrxnSession(session)
  }
}

// Rollback a transaction session
export const rollbackTransactionSession = async (session: ClientSession): Promise<void> => {
  try {
    await session.abortTransaction()
  } catch (error) {
  } finally {
    await endTrxnSession(session)
  }
}

const endTrxnSession = async (session: ClientSession) => {
  try {
    await session.endSession()
  } catch (error) {
    console.error('Transaction session end failed:', error)
  }
}

interface SideEffectTask {
  servicePath: string
  methodName: string
  args: any[]
}

/**
 * Utility function to defer/queue side effects during transactions
 *
 * When a process is running in a transaction, we don't want to execute side effects
 * immediately (for example, sending emails). Instead, we queue them and execute
 * them after the transaction is committed.
 *
 * @example
 * // Instead of executing immediately:
 * this.app.service('notice-tpl').mailto('ReminderWhenMentorSessionEnds', servicer.email, {
 *   username: servicer.nickname,
 *   session_name: name,
 *   url
 * })
 *
 * // Use queueForCommit:
 * queueForCommit(
 *   this.app,
 *   'notice-tpl',
 *   'mailto',
 *   ['ReminderWhenMentorSessionEnds', servicer.email, {
 *     username: servicer.nickname,
 *     session_name: name,
 *     url
 *   }],
 *   params
 * )
 */
export async function queueForCommit(app: any, servicePath: string, methodName: string, args: any[], params: TxnParams = {}): Promise<void> {
  // If inside a transaction, collect the task instead of running it
  if (params.mongoose?.session) {
    if (!params.sideEffectsToExecute) {
      params.sideEffectsToExecute = []
    }
    params.sideEffectsToExecute.push({servicePath, methodName, args})
  } else {
    // If not in a transaction, execute immediately
    const service = app.service(servicePath)

    // Check if methodName contains 'Model.' to handle direct Model operations
    if (methodName.startsWith('Model.')) {
      const modelMethod = methodName.substring(6) // Remove 'Model.' prefix
      await service.Model[modelMethod](...args)
    } else {
      await service[methodName](...args)
    }
  }
}

/**
 * execute all side effects from params.sideEffectsToExecute created via queueForCommit
 * this will be called after transaction is committed via commitTransactionSession/commitTransaction function itself
 * no need to import and call this function manually
 */
async function executeSideEffects(app: any, params: TxnParams): Promise<void> {
  if (!Array.isArray(params.sideEffectsToExecute) || params.sideEffectsToExecute.length === 0) return

  for (const task of params.sideEffectsToExecute) {
    try {
      const service = app.service(task.servicePath)

      // Check if methodName contains 'Model.' to handle direct Model operations
      if (task.methodName.startsWith('Model.')) {
        const modelMethod = task.methodName.substring(6) // Remove 'Model.' prefix
        await service.Model[modelMethod](...task.args)
      } else {
        await service[task.methodName](...task.args)
      }
    } catch (error) {
      console.error('Side effect failed:', error)
    }
  }
}

// Helper function to conditionally await
export const conditionalAwait = async (promise: Promise<any>, condition: boolean) => {
  if (condition) {
    return await promise
  } else {
    promise
    return
  }
}

/*
Usage of "session":

- While calling feathers service methods:
await app.service('users').create({ name: 'John', email: '<EMAIL>' }, { mongoose: { session } });
await app.service('users').patch('1234567890', { email: '<EMAIL>' }, { mongoose: { session } });

- While calling direct mongoose methods (direct db calls):
await app.service('users').Model.create([{ name: 'John', email: '<EMAIL>' }], { session });
await app.service('users').Model.updateOne({ name: 'John' }, { email: '<EMAIL>' }, { session });
await app.service('users').Model.deleteOne({ name: 'John' }, { session });
await app.service('users').Model.findOneAndUpdate({ name: 'John' }, { email: '<EMAIL>' }, { session });

---------

Hook usage example:

export default {
  before: {
    create: [startTransaction()],
    update: [startTransaction()],
    patch: [startTransaction()],
    remove: [startTransaction()]
  },
  after: {
    create: [commitTransaction()],
    update: [commitTransaction()],
    patch: [commitTransaction()],
    remove: [commitTransaction()]
  },
  error: {
    create: [rollbackTransaction()],
    update: [rollbackTransaction()],
    patch: [rollbackTransaction()],
    remove: [rollbackTransaction()]
  }
};
---------
Standalone usage example:

const session = await startTransactionSession(this.app);
try {
  await app.service('users').Model.updateOne({ name: 'John' }, { email: '<EMAIL>' }, { session });
  await app.service('users').create({ name: 'John', email: '<EMAIL>' }, { mongoose: { session } });
  await commitTransactionSession(session);
} catch (error) {
  await rollbackTransactionSession(session);
}
*/
