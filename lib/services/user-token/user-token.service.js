"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const user_token_class_1 = require("./user-token.class");
const user_token_model_1 = __importDefault(require("../../models/user-token.model"));
const user_token_hooks_1 = __importDefault(require("./user-token.hooks"));
function default_1(app) {
    const options = {
        Model: (0, user_token_model_1.default)(app),
        paginate: app.get('paginate')
    };
    // Initialize our service with any options it requires
    app.use('/user-token', new user_token_class_1.UserToken(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('user-token');
    service.hooks(user_token_hooks_1.default);
}
exports.default = default_1;
