"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const authentication = __importStar(require("@feathersjs/authentication"));
const hook_1 = __importDefault(require("../../hook"));
// Don't remove this comment. It's needed to format import lines nicely.
const { authenticate } = authentication.hooks;
const mod = {
    get(d) {
    }
};
exports.default = {
    before: {
        all: [],
        find: [],
        get: [hook_1.default.toClass, (d) => {
                if (d.id && mod[d.id])
                    return mod[d.id](d);
                else
                    return d;
            }],
        create: [authenticate('jwt'), (d) => {
                var _a;
                d.data.uid = (_a = d.params.user) === null || _a === void 0 ? void 0 : _a._id;
            }],
        update: [authenticate('jwt')],
        patch: [authenticate('jwt')],
        remove: [authenticate('jwt')]
    },
    after: {
        all: [],
        find: [async (d) => {
                var _a, _b;
                if (!d.result.data)
                    return;
                d.result.isReview = await d.service.Model.count({ rid: (_a = d.params.query) === null || _a === void 0 ? void 0 : _a.rid, uid: (_b = d.params.user) === null || _b === void 0 ? void 0 : _b._id });
                for (const doc of d.result.data) {
                    doc.user = await d.app.service('users').uidToInfo(doc.uid);
                }
            }],
        get: [],
        create: [],
        update: [],
        patch: [],
        remove: []
    },
    error: {
        all: [],
        find: [],
        get: [],
        create: [],
        update: [],
        patch: [],
        remove: []
    }
};
