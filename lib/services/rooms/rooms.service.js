"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const rooms_class_1 = require("./rooms.class");
const rooms_model_1 = __importDefault(require("../../models/rooms.model"));
const rooms_hooks_1 = __importDefault(require("./rooms.hooks"));
function default_1(app) {
    const options = {
        Model: (0, rooms_model_1.default)(app),
        paginate: app.get('paginate')
    };
    // Initialize our service with any options it requires
    app.use('/rooms', new rooms_class_1.Rooms(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('rooms');
    service.hooks(rooms_hooks_1.default);
}
exports.default = default_1;
