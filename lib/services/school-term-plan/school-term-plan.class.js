"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchoolTermPlan = void 0;
const feathers_mongoose_1 = require("feathers-mongoose");
const { NotFound, GeneralError, BadRequest } = require('@feathersjs/errors');
class SchoolTermPlan extends feathers_mongoose_1.Service {
    //eslint-disable-next-line @typescript-eslint/no-unused-vars
    constructor(options, app) {
        super(options);
        this.app = app;
    }
    async handleConflict({ doc }) {
        let schoolTermPlan = await this.Model.find({ schoolTerm: doc.schoolTerm, _id: { $ne: doc._id } }).lean();
        for (let i = 0; i < schoolTermPlan.length; i++) {
            const item = schoolTermPlan[i];
            let newWeek = item.week.filter((e) => !doc.week.includes(e));
            if (newWeek.length == 0) {
                await this.Model.deleteOne({ _id: item._id });
            }
            if (item.week.length != newWeek.length) {
                await this.Model.updateOne({ _id: item._id }, { $set: { week: newWeek } });
            }
        }
    }
}
exports.SchoolTermPlan = SchoolTermPlan;
