"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const school_user_class_1 = require("./school-user.class");
const school_user_model_1 = __importDefault(require("../../models/school-user.model"));
const school_user_hooks_1 = __importDefault(require("./school-user.hooks"));
function default_1(app) {
    const options = {
        Model: (0, school_user_model_1.default)(app),
        multi: ['create'],
        whitelist: ['$regex', '$options', '$search'],
        paginate: app.get('paginate')
    };
    // Initialize our service with any options it requires
    app.use('/school-user', new school_user_class_1.SchoolUser(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('school-user');
    service.hooks(school_user_hooks_1.default);
}
exports.default = default_1;
