"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const authentication = __importStar(require("@feathersjs/authentication"));
const logger_1 = __importDefault(require("../../logger"));
// Don't remove this comment. It's needed to format import lines nicely.
const { authenticate } = authentication.hooks;
exports.default = {
    before: {
        all: [authenticate('jwt')],
        find: [],
        get: [],
        create: [],
        update: [],
        patch: [],
        remove: [],
    },
    after: {
        all: [],
        find: [],
        get: [],
        create: [
            (d) => {
                const servicer = d.result.servicer[0];
                if (!servicer)
                    return;
                d.app.service('service-conf').patch(servicer, { $inc: { fans: 1 } });
            },
        ],
        update: [],
        patch: [
            (d) => {
                var _a, _b;
                const servicer = ((_a = d.data.$addToSet) === null || _a === void 0 ? void 0 : _a.servicer) || ((_b = d.data.$pull) === null || _b === void 0 ? void 0 : _b.servicer);
                if (!servicer)
                    return;
                const fans = d.data.$addToSet ? 1 : -1;
                d.app.service('service-conf').patch(servicer, { $inc: { fans } });
            },
        ],
        remove: [],
    },
    error: {
        all: [],
        find: [],
        get: [
            async (d) => {
                var _a;
                if (d.error.code === 404) {
                    // 不存在，自动创建
                    d.error = null;
                    d.result = await d.service.create({ _id: (_a = d.params.user) === null || _a === void 0 ? void 0 : _a._id }, d.params);
                }
            },
        ],
        create: [],
        update: [],
        patch: [
            async (d) => {
                var _a, _b;
                if (d.error.code === 404) {
                    // 不存在，自动创建
                    const servicer = [(_a = d.data.$addToSet) === null || _a === void 0 ? void 0 : _a.servicer];
                    if (!Acan.isEmpty(servicer)) {
                        logger_1.default.info(servicer, 'create');
                        d.error = null;
                        d.result = await d.service.create({ _id: (_b = d.params.user) === null || _b === void 0 ? void 0 : _b._id, servicer }, d.params);
                    }
                }
            },
        ],
        remove: [],
    },
};
