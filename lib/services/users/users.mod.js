"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.fn = void 0;
const logger_1 = __importDefault(require("../../logger"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const authentication = __importStar(require("@feathersjs/authentication"));
const { authenticate } = authentication.hooks;
const fn = {
    async redisHcache(d, key, hash, dbGet) {
        const redisClient = d.app.get('redis');
        let json = await redisClient.HGET(key, hash);
        if (!Acan.isEmpty(json))
            return JSON.parse(json);
        logger_1.default.info('redisHcache from db', key, hash);
        const rs = await dbGet();
        if (!rs)
            return rs;
        if (typeof hash === 'object')
            hash = hash.toString();
        redisClient.HSET(key, hash, JSON.stringify(rs));
        return rs;
    },
    // async findByEmail(d: HookContext, email: string) {
    //   let rs = await d.app.service('users').Model.findOne({email}).select(['nickname', 'avatar', 'email', 'uid'])
    //   return rs
    // },
    // async findByOldUid(d: HookContext, uid: string) {
    //   const rs = await d.app.service('users').Model.findOne({uid})
    //   return rs
    // },
    async emailToUid(d, email) {
        return await fn.redisHcache(d, 'email:_id', email, async () => {
            let rs = Acan.clone(await d.app.service('users').Model.findOne({ email }).select(['email']));
            return rs === null || rs === void 0 ? void 0 : rs._id;
        });
    },
    // async uidToOldUid(d: HookContext, _id: string) {
    //   return await fn.redisHcache(d, '_id:oldUid', _id, async () => {
    //     const rs = Acan.clone(await d.app.service('users').Model.findById(_id).select(['uid']))
    //     return rs?.uid || rs?._id
    //   })
    // },
    async uidToInfo(d, _id) {
        // uid = users._id
        return await fn.redisHcache(d, '_id:info', _id, async () => {
            const rs = await d.app.service('users').Model.findById(_id).select(['uid', 'nickname', 'avatar', 'email']);
            logger_1.default.info(rs, _id);
            return rs;
        });
    },
};
exports.fn = fn;
const mod = {
    async userInfo(d) {
        var _a;
        const { school, uid } = (_a = d.params.query) !== null && _a !== void 0 ? _a : {};
        const owner = await fn.uidToInfo(d, uid);
        const rs = { ...owner };
        if (school) {
            const srs = await d.app.service('school-user').getInfo({ school, email: owner.email });
            Object.assign(rs, srs || {});
            rs.schoolInfo = await d.app.service('school-plan').getInfo(d, school);
        }
        d.result = rs;
    },
    async export(d) {
        var _a;
        const { arr } = (_a = d.params.query) !== null && _a !== void 0 ? _a : {};
        d.result = await d.service.Model.find({ _id: { $in: arr } }).select('email');
    },
    async clearGoogle(d) {
        var _a;
        const { google } = (_a = d.params.query) !== null && _a !== void 0 ? _a : {};
        if (!google)
            return (d.result = null), d;
        const redis = d.app.get('redis');
        redis.del(`googleAuth:${google}`);
        redis.del(`googleRT:${google}`);
        d.result = await d.app.service('user-token').Model.deleteOne({ sub: google });
    },
    async jwtOld(d) {
        var _a, _b;
        await authenticate('jwt')(d);
        const redisOld = d.app.get('redisOld');
        const query = (_a = d.params.query) !== null && _a !== void 0 ? _a : {};
        const { secret } = d.app.get('authentication');
        query.exp = Math.floor(Date.now() / 1000) + 86400 * 7;
        const user = (_b = d.params.user) !== null && _b !== void 0 ? _b : {};
        if (!query.username)
            query.username = user.username || user.email;
        logger_1.default.info('jwtOld: ', query);
        const token = jsonwebtoken_1.default.sign(query, secret, { header: { typ: 'JWT', alg: 'HS256' }, noTimestamp: true });
        const rs = await redisOld.get(`prefix_user_logout_token_${token}`);
        d.result = { query, token, rs };
    },
    async jwtNew(d) {
        var _a;
        await authenticate('jwt')(d);
        const query = (_a = d.params.query) !== null && _a !== void 0 ? _a : {};
        const rs = await d.app.service('authentication').create({ strategy: 'sync' }, {
            payload: { sid: '/' },
            user: { ...query, id: query._id },
        });
        d.result = { ...rs };
    },
    async get(d) {
        d.result = { n: 0 };
    },
    // async acade(d: HookContext) {
    //   await authenticate('jwt')(d)
    //   const user = d.params.user ?? {}
    //   const sysUser = await getSysUser(d)
    //   const {id, school} = d.params.query ?? {}
    //   if (!id) return Promise.reject({message: 'No id!'})
    //   if (!sysUser.id) return Promise.reject({message: 'No sys user!'})
    //   const where: any = school ? {school_id: school} : {user_id: sysUser.id}
    //   let rs
    //   rs = await knexj('cc_academic_setting_curriculum').where(where).select(['curriculum_name', 'curriculum_id'])
    //   const list: any = {}
    //   rs.map((v: any) => {
    //     list[v.curriculum_id] = {name: v.curriculum_name, grades: [], subjects: []}
    //   })
    //   rs = await knexj('cc_academic_setting_grade').where(where).select(['curriculum_id', 'official_grade_name', 'grade_name', 'age'])
    //   rs.map((v: any) => {
    //     const o = {name: v.official_grade_name, alias: v.grade_name, age: v.age}
    //     list[v.curriculum_id].grades.push(o)
    //   })
    //   rs = await knexj('cc_academic_setting_subject').where(where).select(['curriculum_id', 'subject_name'])
    //   rs.map((v: any) => {
    //     const o = {name: v.subject_name}
    //     list[v.curriculum_id].subjects.push(o)
    //   })
    //   d.result = list
    // },
};
exports.default = mod;
