"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const students_class_1 = require("./students.class");
const students_model_1 = __importDefault(require("../../models/students.model"));
const students_hooks_1 = __importDefault(require("./students.hooks"));
function default_1(app) {
    const options = {
        Model: (0, students_model_1.default)(app),
        multi: ['create'],
        whitelist: ['$regex', '$options', '$search', '$elemMatch'],
        paginate: app.get('paginate'),
    };
    // Initialize our service with any options it requires
    app.use('/students', new students_class_1.Students(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('students');
    service.hooks(students_hooks_1.default);
}
exports.default = default_1;
