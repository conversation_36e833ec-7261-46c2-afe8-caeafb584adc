"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserToken = void 0;
const feathers_mongoose_1 = require("feathers-mongoose");
const got = require('got');
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const logger_1 = __importDefault(require("../../logger"));
class UserToken extends feathers_mongoose_1.Service {
    //eslint-disable-next-line @typescript-eslint/no-unused-vars
    constructor(options, app) {
        super(options);
        this.app = app;
    }
    async getInfo({ type }, params) {
        var _a, _b;
        let sub = (_a = params.user) === null || _a === void 0 ? void 0 : _a[type];
        if (!sub) {
            const user = await this.app.service('users').Model.findById((_b = params.user) === null || _b === void 0 ? void 0 : _b._id);
            sub = user[type];
        }
        if (!sub)
            return {}; // 没有绑定过
        return await this.Model.findOne({ type, sub });
    }
    async zoomReq(href) {
        const zoom = this.app.get('zoom');
        const Authorization = 'Basic ' + Acan.base64encode(`${zoom.id}:${zoom.secret}`);
        return (await got
            .post(href, {
            headers: { Authorization, 'Content-Type': 'application/x-www-form-urlencoded' },
            json: true,
        })
            .catch((err) => {
            console.log(err, 'zoomReq');
            return {};
        })).body;
    }
    // 必须退出zoom账号，下次授权才能勾选确认
    async zoomRevoke(result, isRe = false) {
        const ubj = new URL('https://zoom.us/oauth/revoke');
        ubj.searchParams.set('token', result.token);
        const rs = await this.zoomReq(ubj.href);
        if ((rs === null || rs === void 0 ? void 0 : rs.reason) === 'Invalid Token' && !isRe) {
            const old = Acan.clone(result);
            delete old._id;
            const data = await this.refreshToken(old);
            if (data === null || data === void 0 ? void 0 : data.token)
                return await this.zoomRevoke(data, true);
        }
        return rs;
    }
    async refreshToken(old) {
        const ubj = new URL('https://zoom.us/oauth/token?grant_type=refresh_token');
        ubj.searchParams.set('refresh_token', old.rt);
        logger_1.default.info('zoom refreshToken', ubj.href);
        const rs = await this.zoomReq(ubj.href);
        if (rs === null || rs === void 0 ? void 0 : rs.reason)
            return rs;
        if (!(rs === null || rs === void 0 ? void 0 : rs.access_token))
            return logger_1.default.warn('refreshToken error', rs, ubj.href), null;
        // logger.info('refreshToken ok', rs)
        old.token = rs.access_token;
        if (rs.refresh_token)
            old.rt = rs.refresh_token;
        const payload = jsonwebtoken_1.default.decode(old.token);
        old.exp = payload.exp;
        const post = { token: rs.access_token, exp: payload.exp, rt: rs.refresh_token };
        if (old._id) {
            const urs = await this.app.service('user-token').Model.updateOne({ _id: old._id }, post, { upsert: true });
            // logger.info('zoom up:', urs, post)
        }
        return post;
    }
}
exports.UserToken = UserToken;
