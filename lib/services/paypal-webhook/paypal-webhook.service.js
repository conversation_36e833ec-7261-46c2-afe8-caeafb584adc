"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const paypal_webhook_class_1 = require("./paypal-webhook.class");
const paypal_webhook_model_1 = __importDefault(require("../../models/paypal-webhook.model"));
const paypal_webhook_hooks_1 = __importDefault(require("./paypal-webhook.hooks"));
function default_1(app) {
    const options = {
        Model: (0, paypal_webhook_model_1.default)(app),
        paginate: app.get('paginate')
    };
    // Initialize our service with any options it requires
    app.use('/paypal-webhook', new paypal_webhook_class_1.PaypalWebhook(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('paypal-webhook');
    service.hooks(paypal_webhook_hooks_1.default);
}
exports.default = default_1;
