"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const authentication = __importStar(require("@feathersjs/authentication"));
const hook_1 = __importDefault(require("../../hook"));
// Don't remove this comment. It's needed to format import lines nicely.
const { authenticate } = authentication.hooks;
exports.default = {
    before: {
        all: [],
        find: [
            async (d) => {
                var _a, _b;
                const query = d.params.query || {};
                if (!((_a = query._id) === null || _a === void 0 ? void 0 : _a.$in) && !query.school)
                    await hook_1.default.sysQuery('school')(d);
                query.$select = ((_b = d.params.query) === null || _b === void 0 ? void 0 : _b.$select) || ['name', 'school', 'mode', 'curriculum', 'curricId', 'group', 'createdAt', 'updatedAt'];
            },
        ],
        get: [hook_1.default.toClass],
        create: [authenticate('jwt')],
        update: [authenticate('jwt'), hook_1.default.disable],
        patch: [authenticate('jwt'), hook_1.default.toClass],
        remove: [authenticate('jwt')],
    },
    after: {
        all: [],
        find: [
            (d) => {
                if (isDev)
                    d.result.query = d.params.query;
            },
        ],
        get: [
            async (d) => {
                if (hook_1.default.classExist(d))
                    return d;
                await d.service.mergePubTpl(d.result); // 合并公共模板配置
            },
        ],
        create: [],
        update: [],
        patch: [],
        remove: [],
    },
    error: {
        all: [],
        find: [],
        get: [],
        create: [],
        update: [],
        patch: [],
        remove: [],
    },
};
