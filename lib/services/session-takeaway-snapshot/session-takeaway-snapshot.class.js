"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SessionTakeawaySnapshot = void 0;
const feathers_mongoose_1 = require("feathers-mongoose");
const { NotFound, GeneralError, BadRequest } = require('@feathersjs/errors');
class SessionTakeawaySnapshot extends feathers_mongoose_1.Service {
    //eslint-disable-next-line @typescript-eslint/no-unused-vars
    constructor(options, app) {
        super(options);
        this.app = app;
    }
    // Inform parents to view takeaway
    async getInformParents({ _id, hash, studentId }, params) {
        let takeaway = await this.Model.findById(_id).select(['uid', 'session', 'hash']);
        // let session: any = await this.app.service('session').Model.findOne({_id: takeaway.session})
        // let student: any = await this.app.service('students').Model.findOne({uid: takeaway.uid, school: session.school})
        if (takeaway.hash == hash)
            return Promise.reject(new GeneralError('Same hash'));
        // 首次发送
        if (!takeaway.hash && hash) {
            await this.app.service('session').Model.updateOne({ _id: takeaway.session }, { $inc: { 'count.report': 1 } });
        }
        return await this.Model.updateOne({ _id }, { $set: { hash: hash } });
        // if (!student) return Promise.reject(new GeneralError('Not find student'))
        // const url = `${SiteUrl}/v2/account/takeaway/view/${_id}?studentId=${studentId}`
        // return await this.app.service('notice-tpl').mailto('InformParentsToViewTakeaway', student.parent.email, {
        //   username: student.parent.name.join(' '),
        //   student_name: student.name.join(' '),
        //   session_name: session.name,
        //   pwd: hash.slice(-4),
        //   url,
        // })
    }
}
exports.SessionTakeawaySnapshot = SessionTakeawaySnapshot;
