"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const tool_class_1 = require("./tool.class");
const tool_hooks_1 = __importDefault(require("./tool.hooks"));
function default_1(app) {
    const options = {
        paginate: app.get('paginate')
    };
    // Initialize our service with any options it requires
    app.use('/tool', new tool_class_1.Tool(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('tool');
    service.hooks(tool_hooks_1.default);
}
exports.default = default_1;
