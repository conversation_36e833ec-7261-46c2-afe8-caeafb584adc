"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const subjects_class_1 = require("./subjects.class");
const subjects_model_1 = __importDefault(require("../../models/subjects.model"));
const subjects_hooks_1 = __importDefault(require("./subjects.hooks"));
const logger_1 = __importDefault(require("../../logger"));
function default_1(app) {
    const options = {
        Model: (0, subjects_model_1.default)(app),
        whitelist: ['$exists', '$regex', '$options', '$search'],
        paginate: app.get('paginate'),
    };
    // Initialize our service with any options it requires
    app.use('/subjects', new subjects_class_1.Subjects(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('subjects');
    service.hooks(subjects_hooks_1.default);
    setTimeout(async () => {
        const arr = await service.Model.find({ uid: '1', curriculum: 'pd' }).select('subjectCode name participants');
        const pdSubjectCodeMap = {};
        for (const o of arr) {
            pdSubjectCodeMap[o.subjectCode] = o;
        }
        logger_1.default.info('pd subjects:', pdSubjectCodeMap);
    }, 1000);
    // 加载Steam topic第一层数据作为 subject匹配
    // service.Model.find({uid: '1', isLib: true, subjectCode: 'steam'})
    //   .select(['_id'])
    //   .then(async (arr: any) => {
    //     if (Acan.isEmpty(arr)) return
    //     const {snapshot}: any = await service.Model.findById(arr[0]._id).select(['snapshot.topic'])
    //     const subjectMap: any = {}
    //     for (const o of snapshot.topic) {
    //       subjectMap[o._id] = o.name
    //     }
    //     logger.info(subjectMap, 111)
    //   })
}
exports.default = default_1;
