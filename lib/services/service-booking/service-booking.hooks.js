"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const authentication = __importStar(require("@feathersjs/authentication"));
const hook_1 = __importDefault(require("../../hook"));
// Don't remove this comment. It's needed to format import lines nicely.
const { authenticate } = authentication.hooks;
exports.default = {
    before: {
        all: [],
        find: [
            authenticate('jwt'),
            async (d) => {
                const query = d.params.query;
                // tab: booker: 预订列表, servicer: 服务者看的预约列表
                await hook_1.default.sysQuery(query.tab)(d);
            },
        ],
        get: [
            hook_1.default.toClass,
            (d) => {
                if (!Acan.isEmpty(d.params.query)) {
                    d.params.extQuery = d.params.query;
                    d.params.query = {};
                }
            },
        ],
        create: [
            authenticate('jwt'),
            async (d) => {
                var _a;
                d.data.booker = (_a = d.params.user) === null || _a === void 0 ? void 0 : _a._id;
                delete d.data.session;
                await d.service.checkHours(d.data, d.params);
                // 判断服务包次数是否足够
                await d.app.service('service-pack-user').checkTimes({ _id: d.data.packUser, times: parseInt(d.data.times) });
                // 获取服务包类型
                const { snapshot } = await d.app.service('service-pack-user').Model.findById(d.data.packUser).select(['snapshot.type', 'snapshot.mentoringType']);
                Object.assign(d.data, snapshot);
            },
        ],
        update: [hook_1.default.disable],
        patch: [authenticate('jwt'), hook_1.default.toClass],
        remove: [authenticate('jwt'), hook_1.default.disable],
    },
    after: {
        all: [],
        find: [
            async (d) => {
                for (const o of d.result.data) {
                    await d.service.extUser(o);
                }
                if (isDev)
                    d.result.query = d.params.query;
                return d;
            },
        ],
        get: [
            async (d) => {
                if (hook_1.default.classExist(d))
                    return d;
                await d.service.extUser(d.result);
                await d.service.extSession(d.result, d.params);
            },
        ],
        create: [
            async (d) => {
                const { _id, type, packUser, servicePackApply, times, servicer, start, oldSession } = d.result;
                // 创建预约，扣除服务包次数 service-pack-user.used += service-booking.times
                await d.app.service('service-pack-user-data').used({ booking: _id.toString(), packUser, times, type: 'booking', servicer, oldSession, start }, d.params);
                if (type === 'content')
                    await d.service.importLecture(d.result); // Lecture 自动排课 https://github.com/zran-nz/bug/issues/79
                else if (servicePackApply)
                    await d.service.autoCreateSession(d.result); // 面试服务包自动排课
            },
        ],
        update: [],
        patch: [
            (d) => {
                if (hook_1.default.classExist(d))
                    return d;
            },
        ],
        remove: [],
    },
    error: {
        all: [],
        find: [],
        get: [],
        create: [],
        update: [],
        patch: [],
        remove: [],
    },
};
