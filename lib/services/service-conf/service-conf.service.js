"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const service_conf_class_1 = require("./service-conf.class");
const service_conf_model_1 = __importDefault(require("../../models/service-conf.model"));
const service_conf_hooks_1 = __importDefault(require("./service-conf.hooks"));
function default_1(app) {
    const options = {
        Model: (0, service_conf_model_1.default)(app),
        whitelist: ['$elemMatch', '$exists', '$regex', '$options'],
        paginate: app.get('paginate'),
    };
    // Initialize our service with any options it requires
    app.use('/service-conf', new service_conf_class_1.ServiceConf(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('service-conf');
    service.hooks(service_conf_hooks_1.default);
}
exports.default = default_1;
