import {Application} from '../../../declarations'
import {commitTransactionSession, rollbackTransactionSession, startTransactionSession} from '../../../dbTransactions'

declare const SiteUrl: string
declare const hashToUrl: (hash: string) => string

export class OrderRecovery {
  adminEmails: string[]
  constructor(private app: Application) {
    this.adminEmails = ['<EMAIL>']
  }

  /**
   * Main entry point for order completion, after successful payment.
   * @param orderId The ID of the order to process.
   * @param paymentDetails Payment information, only used on the first API call.
   * @param source The source of the call: 'api' or 'webhook'.
   */
  async processOrderCompletion(orderId: string, paymentDetails: any, source: 'api' | 'webhook') {
    const lockDurationMinutes = 3
    const lockExpiresAt = new Date(Date.now() + lockDurationMinutes * 60 * 1000)

    const originalOrder: any = await this.app
      .service('order')
      .Model.findOneAndUpdate(
        {
          _id: orderId,
          status: {$in: [100, 110]},
          $or: [{processingLockExpiresAt: {$exists: false}}, {processingLockExpiresAt: {$lt: new Date()}}],
        },
        {
          $set: {
            processingLockExpiresAt: lockExpiresAt,
            status: 110,
          },
        }
        // We do NOT use { new: true }, so we get the document *before* this update.
      )
      .lean()
    if (!originalOrder) {
      if (source === 'webhook') {
        const orderInfo = (await this.app.service('order').Model.findById(orderId).select('status')) as any
        if ([100, 110].includes(orderInfo?.status)) throw new Error(`Order ${orderId} is locked. Webhook will retry.`)
      }
      return
    }

    let orderToProcess = originalOrder

    // This is only done if this is the first time the function is running for this order.
    if (!originalOrder.settled) {
      // this triggers a websocket event to notify the frontend
      orderToProcess = await this.app.service('order').patch(orderId, paymentDetails)
    }
    if (source === 'api') {
      // when calling via REST api, we don't wait for the detailed processing to finish
      // this is done to show the user quick response that the payment was successful, and the order is processing
      this.detailedProcessing(orderToProcess)
    } else {
      await this.detailedProcessing(orderToProcess)
    }
  }

  async detailedProcessing(order: any) {
    const maxRetryCount = 3
    const currentRetryCount = order.retryInfo?.count || 0
    const orderId = order._id
    const session = await startTransactionSession(this.app)
    let refundInfo: any = null

    try {
      const transactionParams = {mongoose: {session}, sideEffectsToExecute: []}

      refundInfo = await this.app.service('order').completeOrder(order, transactionParams)
      await commitTransactionSession(session, this.app, transactionParams)

      // Process any refunds *after* the transaction is safely committed
      if (refundInfo.refundPrice > 0 && refundInfo.invalidLinks?.length > 0) {
        await this.processRefund(orderId, refundInfo)
      }
    } catch (error: any) {
      await rollbackTransactionSession(session)

      // --- Error Analysis ---
      let decision = 'REFUND' // Default to refund for safety
      let retryReason = ''

      if (error?.hasErrorLabel && (error.hasErrorLabel('TransientTransactionError') || error.hasErrorLabel('UnknownTransactionCommitResult'))) {
        decision = 'RETRY'
        retryReason = error.hasErrorLabel('TransientTransactionError') ? 'TransientTransactionError' : 'UnknownTransactionCommitResult'
        retryReason += ' - ' + error?.message
      } else if (['ECONNRESET', 'ETIMEDOUT', 'ECONNREFUSED'].includes(error?.code)) {
        decision = 'RETRY'
        retryReason = 'NetworkIssue - ' + error?.message
      } else {
        decision = 'REFUND'
      }

      if (decision === 'RETRY' && currentRetryCount < maxRetryCount) {
        await this.app
          .service('order')
          .Model.updateOne({_id: orderId}, {$inc: {'retryInfo.count': 1}, $set: {'retryInfo.reason': retryReason, 'retryInfo.updatedAt': new Date()}})
        throw error // Re-throw the original error to trigger the webhook retry
      } else {
        // This block is reached for permanent errors OR if max retries have been exceeded.
        await this.issueFullRefund(order)

        // IMPORTANT: We do NOT re-throw the error here. This stops the webhook from
        // retrying an order that will never succeed.
      }
    } finally {
      // --- Release the Lock ---
      // This ensures that if the process succeeded, failed, or even crashed,
      // the next webhook retry can acquire the lock after the timeout.
      await this.app.service('order').Model.updateOne({_id: orderId}, {$unset: {processingLockExpiresAt: ''}})
    }
  }

  // Issue full refund for all products in the order
  async issueFullRefund(order: any) {
    try {
      if (!Array.isArray(order.links)) return

      // Calculate total refund amount and collect product details
      let totalRefundAmount = 0
      const refundLinkNames: string[] = []
      const refundLinkCovers: string[] = []
      let myLinks = Acan.clone(order.links)
      // Process each product in the order
      for (const link of myLinks || []) {
        // if (!link.removed) {
        // Add to total refund amount
        totalRefundAmount += link.price || 0

        // Collect product names for refund description
        if (link.name) {
          refundLinkNames.push(link.name)
        }

        // Collect product covers/images for refund reference
        if (link.cover) {
          refundLinkCovers.push(link.cover)
        }
        link.removed = true
        link.refundPrice = link.price
        // }
      }
      await this.app.service('order').patch(order._id, {
        status: 504,
        paid: 2,
        links: myLinks,
        refundRequired:
          totalRefundAmount > 0
            ? {
                refundPrice: totalRefundAmount,
                invalidLinks: order.links,
              }
            : undefined,
      })
      if (totalRefundAmount > 0) {
        const refundInfo = {
          refundPrice: totalRefundAmount,
          refundLinkName: refundLinkNames,
          refundLinkCover: refundLinkCovers,
        }
        await this.processRefund(order._id, refundInfo, order)
      }
    } catch (refundError) {
      this.app.service('order').Model.updateOne(
        {_id: order._id},
        {
          status: 504,
          paid: 2,
          refundRequired: {
            refundPrice: order.links.reduce((prev: any, cur: any) => {
              return prev + (cur.price || 0)
            }, 0),
            invalidLinks: order.links,
          },
        }
      )
    }
  }

  // Process refund for invalid links
  async processRefund(orderId: string, refundInfo: any, orderDoc?: any, isRetry?: boolean) {
    try {
      const order: any = orderDoc || (await this.app.service('order').Model.findOne({_id: orderId}))
      if (!order) {
        return
      }

      const {refundPrice, refundLinkName, refundLinkCover} = refundInfo
      let refundSuccess = false
      // throw new Error('Error in refund')
      // Process refund via payment provider
      if (order.payMethod.indexOf('paypal') > -1) {
        const refundResult: any = await this.app.service('paypal').get('refund', {
          query: {id: order.paypalId, amount: (refundPrice / 100).toFixed(2)},
        })
        if (refundResult.success) {
          refundSuccess = true
        }
      } else if (order.payMethod.indexOf('braintree') > -1) {
        refundSuccess = true
      }
      // throw new Error('Error after refund')
      if (refundSuccess) {
        await this.handleRefundSuccess(order, refundPrice, refundLinkName, refundLinkCover)
      } else {
        if (isRetry) throw new Error('Refund failed')
        await this.handleRefundFailure(orderId, refundPrice, refundLinkName)
      }
    } catch (error) {
      if (isRetry) throw new Error('Refund failed')
      await this.handleRefundFailure(orderId, refundInfo.refundPrice, refundInfo.refundLinkName)
    }
  }

  // Handle successful refund
  async handleRefundSuccess(order: any, refundPrice: number, refundLinkName: string[], refundLinkCover: string[]) {
    try {
      let refundList: any = []
      let notifyCustomer = false

      if (order.payMethod.indexOf('paypal') > -1) {
        refundList.push({
          method: 'paypal',
          amount: refundPrice,
          createdAt: new Date(),
          executedAt: new Date(),
          executed: true,
          status: 503,
        })
        notifyCustomer = true
      } else if (order.payMethod.indexOf('braintree') > -1) {
        refundList.push({
          method: 'braintree',
          amount: refundPrice,
          createdAt: new Date(),
          executed: false,
          status: 503,
        })
      }
      await this.app.service('order').patch(order._id, {
        $push: {refund: {$each: refundList}},
        $unset: {refundRequired: ''},
      })

      if (notifyCustomer) {
        // Send success notification to customer
        await this.sendRefundSuccessNotification(order, refundPrice, refundLinkName, refundLinkCover)
      }
    } catch (error) {}
  }

  // Handle failed refund
  async handleRefundFailure(orderId: string, refundPrice: number, refundLinkName: string[]) {
    // Send admin notification for manual intervention
    await this.sendRefundFailureAdminNotification(orderId, refundPrice)

    // Send customer notification about delay
    await this.sendRefundDelayCustomerNotification(orderId, refundLinkName)
  }

  // Send refund success notification to customer
  async sendRefundSuccessNotification(order: any, refundPrice: number, refundLinkName: string[], refundLinkCover: string[]) {
    try {
      const userId = order.isSchool ? order.schoolAdmin : order.buyer
      const user = await this.app.service('users').uidToInfo(userId)
      const url = `${SiteUrl}/v2/order/payHistory/${order._id}`
      const url2 = `${SiteUrl}/v2/order/detail/${order._id}`
      await this.app.service('notice-tpl').send(
        'OrderRefundSuccess',
        {_id: user._id, email: user.email},
        {
          username: user.name.join(' '),
          gift_card_amount: '0.00',
          cash_amount: (refundPrice / 100).toFixed(2),
          no: order.no,
          amount: (order.price / 100).toFixed(2),
          date: new Date(),
          url: url,
          link_name: refundLinkName.join(', '),
          url2: url2,
          image: hashToUrl(refundLinkCover[0] || ''),
          addons: refundLinkName.length > 1 ? `+${refundLinkName.length - 1} products` : '',
        }
      )
    } catch (error) {}
  }

  // Send refund delay notification to customer
  async sendRefundDelayCustomerNotification(orderId: string, refundLinkName: string[]) {
    try {
      const order: any = await this.app.service('order').Model.findOne({_id: orderId})
      const userId = order.isSchool ? order.schoolAdmin : order.buyer
      const user = await this.app.service('users').uidToInfo(userId)
      const url = `${SiteUrl}/v2/order/detail/${orderId}`

      await this.app.service('notice-tpl').send(
        'OrderRefundInProcess',
        {_id: user._id, email: user.email},
        {
          username: user.name.join(' '),
          link_name: refundLinkName.join('<br>'),
          url: url,
        }
      )
    } catch (error) {}
  }

  // Send admin notification for refund failure
  async sendRefundFailureAdminNotification(orderId: string, refundPrice: number) {
    try {
      await Promise.all(
        this.adminEmails.map(async (email) => {
          await this.app.service('notice-tpl').send(
            'OrderRefundFailed',
            {_id: '', email: email},
            {
              orderId: orderId,
              amount: refundPrice,
              url: `${SiteUrl}/v2/order/detail/${orderId}`,
              sys_url: `${SiteUrl}/v2/sys/order-failure-logs`,
            }
          )
        })
      )
    } catch (error) {}
  }

  // Retry failed refunds - cron job method
  async retryFailedRefunds() {
    try {
      // Find orders with pending refunds (older than 10 minutes to avoid immediate retries)
      const fiveMinutesAgo = new Date(Date.now() - 10 * 60 * 1000)
      const ordersWithPendingRefunds = await this.app
        .service('order')
        .Model.find({
          'refundRequired.createdAt': {$lt: fiveMinutesAgo},
          'refundRequired.escalated': false,
        })
        .sort({'refundRequired.createdAt': 1})
        .limit(50)

      for (const orderDoc of ordersWithPendingRefunds) {
        try {
          const order: any = orderDoc
          const refundInfo = {
            refundPrice: order.refundRequired.refundPrice,
            invalidLinks: order.refundRequired.invalidLinks,
            refundLinkName: order.refundRequired.refundLinkName || order.refundRequired.invalidLinks?.map((link: any) => link.name) || [],
            refundLinkCover: order.refundRequired.refundLinkCover || order.refundRequired.invalidLinks?.map((link: any) => link.cover) || [],
          }
          // For PayPal orders, check if refund already exists before retrying
          if (order.paypalId) {
            try {
              const refundCheck = await this.app.service('paypal').checkRefundExists(order.paypalId)
              if (refundCheck.exists) {
                await this.handleRefundSuccess(order, refundInfo.refundPrice, refundInfo.refundLinkName, refundInfo.refundLinkCover)
                continue
              }
            } catch (error) {
              // Continue with retry if we can't check status
            }
          }

          await this.processRefund(order._id.toString(), refundInfo, orderDoc, true)
        } catch (error) {
          const order: any = orderDoc

          // If refund has been pending for more than 24 hours, escalate to admin
          const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000)
          if (order.refundRequired.createdAt < twentyFourHoursAgo) {
            await this.sendRefundFailureAdminNotification(order._id.toString(), order.refundRequired.refundPrice)

            // Mark as escalated to prevent repeated admin notifications
            await this.app.service('order').Model.updateOne({_id: order._id}, {$set: {'refundRequired.escalated': true}})
          }
        }
      }
    } catch (error) {}
  }
}
