"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.typeMap = void 0;
const authentication = __importStar(require("@feathersjs/authentication"));
const { NotFound, GeneralError, BadRequest } = require('@feathersjs/errors');
// Don't remove this comment. It's needed to format import lines nicely.
const hook_1 = __importDefault(require("../../hook"));
const { authenticate } = authentication.hooks;
exports.typeMap = {
    ['workshop']: 'Premium workshop',
    ['_content']: 'Premium content',
    ['mentoring:essay']: 'Essay',
    ['mentoring:overseasStudy']: 'Overseas study',
    ['mentoring:academic']: 'Academic',
    ['mentoring:teacherTraining']: 'Teacher training',
    ['substitute']: 'Substitute teacher',
    ['correcting']: 'Correcting service',
};
exports.default = {
    before: {
        all: [authenticate('jwt')],
        find: [],
        get: [hook_1.default.toClass],
        create: [
            async (d) => {
                let { booking } = d.data;
                let bookingData = await d.app.service('service-booking').get(booking);
                let servicePack = bookingData.servicePackUser.snapshot;
                let key = `${servicePack.type}${servicePack.mentoringType ? ':' + servicePack.mentoringType : ''}`;
                d.data.serviceBooking = booking;
                d.data.servicePackUser = bookingData.packUser;
                d.data.service = servicePack._id;
                d.data.serviceType = `Mentor service-${exports.typeMap[key]}`;
                d.data.serviceName = servicePack.name;
            },
        ],
        update: [],
        patch: [
            async (d) => {
                const doc = await d.service.Model.findById(d.id).select('status');
                if (d.data.status === 'approved') {
                    // 审核通过
                    d.params.accident = 1;
                }
                if (doc.status === 'approved' && d.data.status === 'rejected') {
                    // 申诉成功
                    d.params.accident = -1;
                }
            },
        ],
        remove: [],
    },
    after: {
        all: [],
        find: [
            async (d) => {
                for (let i = 0; i < d.result.data.length; i++) {
                    await d.service.extData(d.result.data[i]);
                }
            },
        ],
        get: [
            async (d) => {
                if (hook_1.default.classExist(d)) {
                    return d;
                }
                await d.service.extData(d.result);
            },
        ],
        create: [
            async (d) => {
                let { booking } = d.data;
                let { _id, tags } = d.result;
                await d.app.service('service-booking').patch(booking, { accident: { id: _id, status: 'pending', tags } });
            },
        ],
        update: [],
        patch: [
            async (d) => {
                var _a, _b;
                if ((_b = (_a = d.data) === null || _a === void 0 ? void 0 : _a.$addToSet) === null || _b === void 0 ? void 0 : _b.evidencesStudent) {
                    d.service.send(d.result, 'TeachingAccidentReported', d.params);
                }
                // 审核确认后 需要重新更新统计
                if (d.params.accident) {
                    await d.app.service('service-conf').upRating({ _id: d.result.teacher, accident: d.params.accident });
                }
                let { status } = d.data;
                if (status === 'approved') {
                    d.service.handleApprove({ _id: d.id, data: d.data }, d.params);
                }
                if (status === 'rejected') {
                    d.service.send(d.result, 'TeachingAccidentRejected', d.params);
                    d.app.service('suspend-class').suspend({ type: 'teaching-accident', accidentId: d.id, status, days: 0, uid: d.result.teacher });
                }
                await d.app.service('service-booking').patch(d.result.serviceBooking, { 'accident.status': status });
            },
        ],
        remove: [],
    },
    error: {
        all: [],
        find: [],
        get: [],
        create: [],
        update: [],
        patch: [],
        remove: [],
    },
};
