"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SuspendClass = void 0;
const feathers_mongoose_1 = require("feathers-mongoose");
const { NotFound, GeneralError, BadRequest } = require('@feathersjs/errors');
class SuspendClass extends feathers_mongoose_1.Service {
    //eslint-disable-next-line @typescript-eslint/no-unused-vars
    constructor(options, app) {
        super(options);
        this.app = app;
    }
    async extUser(one, params) {
        if (one.uid) {
            one.userInfo = await this.app.service('users').uidToInfo(one.uid);
        }
    }
    // 停课
    async suspend({ type, accidentId, status = 'approved', days, uid, lag = false }) {
        let suspend = await this.Model.findOne({ uid }).sort({ endAt: -1 });
        let user = await this.app.service('users').uidToInfo(uid);
        let startAt, endAt;
        if (suspend) {
            startAt = new Date(suspend.endAt).getTime();
            endAt = startAt + days * 24 * 60 * 60 * 1000;
        }
        else {
            startAt = new Date().getTime();
            endAt = startAt + days * 24 * 60 * 60 * 1000;
        }
        if (type == 'teaching-accident') {
            let logs = await this.app.service('suspend-logs').Model.find({ uid, accident: accidentId, withdraw: false });
            let logsDays = logs.reduce((acc, cur) => acc + cur.days, 0);
            if (status == 'rejected' && suspend && logs.length > 0) {
                await this.app.service('suspend-logs').Model.updateMany({ uid, accident: accidentId, withdraw: false }, { $set: { withdraw: true } });
                endAt = new Date(endAt).getTime() - logsDays * 24 * 60 * 60 * 1000;
            }
            if (status == 'approved') {
                if (logs.length > 0) {
                    let addDays = days - logsDays;
                    endAt = startAt + addDays * 24 * 60 * 60 * 1000;
                    days = addDays;
                }
            }
        }
        if (status == 'approved') {
            await this.app.service('suspend-logs').Model.create({
                uid,
                nickname: user.name.join(' '),
                type,
                accident: accidentId,
                days: days,
                startAt,
                endAt,
            });
        }
        let update = { nickname: user.name.join(' '), startAt: (suspend === null || suspend === void 0 ? void 0 : suspend.startAt) || startAt, endAt };
        if (lag) {
            update.lag = true;
        }
        await this.Model.updateOne({ uid }, { $set: update }, { upsert: true });
        return { startAt, endAt };
    }
    endExpiration() {
        this.Model.find({ endAt: { $lt: Date.now() } }).then(async (rs) => {
            for (let i = 0; i < rs.length; i++) {
                await this.Model.deleteOne({ _id: rs[i]._id });
            }
        });
    }
    async cron1({}, params) {
        this.endExpiration();
    }
}
exports.SuspendClass = SuspendClass;
