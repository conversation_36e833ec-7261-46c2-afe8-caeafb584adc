"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServiceConf = void 0;
const feathers_mongoose_1 = require("feathers-mongoose");
const logger_1 = __importDefault(require("../../logger"));
const errors_1 = require("@feathersjs/errors");
class ServiceConf extends feathers_mongoose_1.Service {
    //eslint-disable-next-line @typescript-eslint/no-unused-vars
    constructor(options, app) {
        super(options);
        this.app = app;
    }
    // 更新评分数据
    async upRating({ _id, rating, accident }) {
        const doc = await this.Model.findById(_id).select('count');
        if (rating)
            doc.count.rating += rating;
        if (accident)
            doc.count.accident += accident;
        // 计算比率
        doc.count.rate = Math.ceil((1 - doc.count.accident / doc.count.rating) * 100) / 100;
        await this.Model.updateOne({ _id }, { count: doc.count });
    }
    async ext(one) {
        if (!(one === null || one === void 0 ? void 0 : one._id))
            return;
        one.owner = await this.app.service('users').uidToInfo(one._id);
    }
    // 查询老师最近几天的冲突的时间段数据
    async getRecentDaysHours({ uid, days }, params) {
        const start = Date.now() + 12 * 3600000;
        const end = start + days * 86400000;
        // 批量找出最近几天的session, booking
        const booking = await this.app.service('service-booking').getHours({ uid: { $in: uid }, end });
        const session = await this.app.service('session').getHours({ uid: { $in: uid }, end });
        return { booking, session };
    }
    // 老师列表通过服务包查找
    async getTeachersByPack({ $sort, search, packId, packUserId, subject, topic, hours, gradeGroup, countryCode }, params) {
        var _a, _b;
        const packSelect = [
            'type',
            'mentoringType',
            'countryCode',
            'curriculum',
            'subject',
            'topic',
            'gradeGroup',
            'duration',
            'break',
            'qualification',
            'serviceRoles',
        ];
        const packUserSelect = packSelect.map((v) => 'snapshot.' + v);
        // 获取服务包快照数据
        let packDoc;
        let isFree = false;
        if (packUserId) {
            let doc = Acan.clone(await this.app
                .service('service-pack-user')
                .Model.findById(packUserId)
                .select(['payMethod', 'pid', ...packUserSelect]));
            if (!doc)
                return {};
            if (doc.pid) {
                const pdoc = Acan.clone(await this.app
                    .service('service-pack-user')
                    .Model.findById(doc.pid)
                    .select(['payMethod', ...packUserSelect]));
                // Lecture的topic处理
                pdoc.snapshot.topic = doc.snapshot.topic.map((v) => v._id);
                doc = pdoc;
            }
            packDoc = doc.snapshot;
            isFree = doc.payMethod === 'cash';
        }
        else if (packId) {
            packDoc = Acan.clone(await this.app.service('service-pack').Model.findById(packId).select(packSelect));
        }
        else {
            return Promise.reject(new errors_1.GeneralError('param error: packUserId'));
        }
        const AuthQuery = { status: 2 };
        if (packDoc.serviceRoles === 'consultant')
            packDoc.serviceRoles = 'mentoring';
        // 继承服务包下的查询条件
        for (const key in packDoc) {
            if (Acan.isEmpty(packDoc[key]))
                continue;
            if (key === 'topic') {
                AuthQuery['topic._id'] = { $in: packDoc[key] };
            }
            else
                AuthQuery[key] = Array.isArray(packDoc[key]) ? { $in: packDoc[key] } : packDoc[key];
        }
        logger_1.default.info(AuthQuery, packDoc.serviceRoles);
        // 'mentoring', 'substitute', 'correcting'都要匹配consultant的数据 https://github.com/zran-nz/bug/issues/5193
        if (isFree || ['mentoring', 'substitute', 'correcting'].includes(AuthQuery.serviceRoles)) {
            AuthQuery.serviceRoles = { $in: [AuthQuery.serviceRoles, 'consultant'] };
        }
        // academic 不需要过滤 topic https://github.com/zran-nz/bug/issues/5625
        if (packDoc.mentoringType === 'academic')
            delete AuthQuery['topic._id'];
        const max = packDoc.duration + packDoc.break;
        if (!Acan.isEmpty(subject))
            AuthQuery.subject = Array.isArray(subject) ? { $in: subject } : subject;
        // steam下的subject 需要用名称匹配 unit.subjects.values == service-auth.topic.label, service-auth.mentoringType: "steam"
        if (!Acan.isEmpty(topic))
            AuthQuery['topic._id'] = Array.isArray(topic) ? { $in: topic } : topic;
        if (!Acan.isEmpty(gradeGroup))
            AuthQuery.gradeGroup = { $in: gradeGroup };
        if (!Acan.isEmpty(countryCode))
            AuthQuery.countryCode = { $in: countryCode };
        delete AuthQuery._id;
        delete AuthQuery.break;
        delete AuthQuery.duration;
        // 查询已认证的数据
        const arr = Acan.clone(await this.app.service('service-auth').Model.find(AuthQuery).select('uid').limit(10000));
        logger_1.default.info(AuthQuery, arr.length);
        if (Acan.isEmpty(arr))
            return { total: 0, skip: 0, data: [], limit: 10000, AuthQuery };
        const list = {};
        for (const o of arr) {
            if (o.uid === ((_a = params.user) === null || _a === void 0 ? void 0 : _a._id))
                continue; // 排除自己
            list[o.uid] = 1;
        }
        const krr = [AuthQuery.type];
        if (AuthQuery.mentoringType)
            krr.push(AuthQuery.mentoringType);
        // 排除被停课的老师
        const suspends = await this.app
            .service('suspend-class')
            .Model.find({ uid: Object.keys(list) })
            .select(['uid']);
        for (const o of suspends) {
            delete list[o.uid];
        }
        const _id = { $in: Object.keys(list) }; // 匹配符合的认证项的用户id列表
        if (search) {
            const rs = await this.app.service('users').Model.distinct('_id', { _id, name: { $regex: search, $options: 'i' } });
            if (Acan.isEmpty(rs))
                return { total: 0, skip: 0, data: [], limit: 10, query: { _id, search } };
            _id.$in = rs;
        }
        // 查询可预约的老师
        const query = {
            _id,
            'hours.0': { $exists: true }, // 只匹配设置过时间段的老师
            // $or: [{[`enable.${krr.join(':')}`]: true}, {[`enable.${krr.join(':')}`]: null}], // 只匹配已经开启的服务项, 改版被弃用
            // serviceRoles: 'mentoring', // 匹配已经开启的服务项目 #4586, 已经弃用，改为 service-auth.serviceRoles下
        };
        // 指定查询时间段
        if (hours) {
            // 排除假日
            query.holiday = {
                $not: {
                    $elemMatch: {
                        0: { $lte: new Date(hours[0]) },
                        1: { $gte: new Date(hours[1]) },
                    },
                },
            };
            // 匹配可用时间段内
            query.hoursIndex = {
                $elemMatch: {
                    0: { $lte: getUTCHour(hours[0]) },
                    1: { $gte: getUTCHour(hours[1]) },
                },
            };
            // 匹配上课时长是否达标
            query.hoursMax = { $gte: max };
        }
        if ($sort)
            query.$sort = $sort;
        const rs = { total: 0, skip: 0, limit: 10000, data: [] };
        logger_1.default.info(query);
        rs.total += await this.Model.count(query);
        let data = await this.teacherFind(query);
        // 收藏老师置顶
        const fans = await this.app.service('service-fans').Model.findById((_b = params.user) === null || _b === void 0 ? void 0 : _b._id);
        if (!Acan.isEmpty(fans === null || fans === void 0 ? void 0 : fans.servicer)) {
            let trr = []; // 置顶数据
            let drr = [];
            for (const o of data) {
                const isFind = fans.servicer.includes(o._id);
                if (isFind) {
                    trr.push(o);
                }
                else {
                    drr.push(o);
                }
            }
            data = [...trr, ...drr];
        }
        // 免费服务包 education consultant角色 置顶
        if (isFree) {
            let trr = []; // 置顶数据
            let drr = [];
            for (const o of data) {
                const isFind = o.auths.find((v) => v.serviceRoles.includes('consultant'));
                if (isFind) {
                    trr.push(o);
                }
                else {
                    drr.push(o);
                }
            }
            data = [...trr, ...drr];
        }
        rs.data = data;
        if (isDev) {
            rs.query = query;
            rs.ext = { ...AuthQuery, suspends };
        }
        return rs;
    }
    async teacherFind(query) {
        const data = Acan.clone(await this.Model.find(query)
            .select(['rating', 'fans', 'count', 'introduction', 'hours', 'hoursIndex'])
            .sort(Object.assign({}, query.$sort || { lastAuth: -1, _id: -1 }, { lastAuth: -1 })) // 滞后显示排序优先
            .skip(0)
            .limit(10000));
        for (const o of data) {
            o.owner = await this.app.service('users').uidToInfo(o._id);
            o.auths = await this.app.service('service-auth').getListByUid({ uid: o._id });
        }
        return data;
    }
    async getHoursIndex({}) {
        const rs = await this.Model.find({ hoursIndex: null, 'hours.0': { $exists: true } }).select(['hours', 'hoursIndex']);
        const uprs = [];
        for (const o of rs) {
            const hoursIndex = [];
            for (const hour of o.hours) {
                hoursIndex.push([getUTCHour(hour[0]), getUTCHour(hour[1])]);
            }
            uprs.push(await this.Model.updateOne({ _id: o._id }, { $set: { hoursIndex } }));
        }
        return { rs, uprs };
    }
    async getHoursMax({}) {
        const rs = await this.Model.find({ hoursMax: null, 'hours.0': { $exists: true } }).select(['hours']);
        const uprs = [];
        for (const o of rs) {
            let max = 0;
            for (const hour of o.hours) {
                const min = Math.floor((new Date(hour[1]).getTime() - new Date(hour[0]).getTime()) / 60000);
                if (max < min)
                    max = min;
            }
            uprs.push(await this.Model.updateOne({ _id: o._id }, { $set: { hoursMax: max } }));
        }
        return { rs, uprs };
    }
    async getTest({ start, end }) {
        const query = {
            holiday: {
                $not: {
                    $elemMatch: {
                        0: { $lte: new Date(start) },
                        1: { $gte: new Date(end) },
                    },
                },
            },
            hoursIndex: {
                $elemMatch: {
                    0: { $lte: getUTCHour(start) },
                    1: { $gte: getUTCHour(end) },
                },
            },
        };
        const rs = await this.Model.find(query).select(['hours.$']);
        return { rs, query };
    }
    async timeoutRejected() {
        await this.Model.updateMany({ status: { $ne: -1 }, vettingDate: { $lt: Date.now() - 365 * 24 * 3600 * 1000 } }, { status: -1 });
    }
    vettingExpireRemind() {
        // 少于30天提醒
        this.Model.find({
            status: 2,
            $or: [{ vettingReminder: { $exists: false } }, { vettingReminder: false }],
            vettingDate: { $lt: new Date(Date.now() + (30 - 365) * 24 * 3600 * 1000), $gt: new Date(Date.now() - 365 * 24 * 3600 * 1000) },
        }).then(async (rs) => {
            for (let i = 0; i < rs.length; i++) {
                const item = rs[i];
                const user = await this.app.service('users').uidToInfo(item._id);
                this.app.service('notice-tpl').send('PoliceVettingWillExpireSoon', { _id: user._id, email: user.email }, {
                    username: user.name.join(' '),
                    url: `${SiteUrl}/v2/account/teacher/auth/introduction?isUpdateMode=1`,
                });
                await this.Model.updateOne({ _id: item._id }, { $set: { vettingReminder: true } });
            }
        });
        // 已过期提醒
        this.Model.find({
            status: -1,
            $or: [{ vettingExpiredReminder: { $exists: false } }, { vettingExpiredReminder: false }],
            vettingDate: { $lt: new Date(Date.now() - 365 * 24 * 3600 * 1000) },
        }).then(async (rs) => {
            for (let i = 0; i < rs.length; i++) {
                const item = rs[i];
                const user = await this.app.service('users').uidToInfo(item._id);
                this.app.service('notice-tpl').send('PoliceVettingHasExpired', { _id: user._id, email: user.email }, {
                    username: user.name.join(' '),
                    url: `${SiteUrl}/v2/account/teacher/auth/introduction`,
                });
                await this.Model.updateOne({ _id: item._id }, { $set: { vettingExpiredReminder: true } });
            }
        });
    }
    async cron1({}, params) {
        this.timeoutRejected();
        this.vettingExpireRemind();
    }
}
exports.ServiceConf = ServiceConf;
