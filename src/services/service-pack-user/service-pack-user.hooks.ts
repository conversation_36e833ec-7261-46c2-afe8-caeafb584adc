import {HookContext} from '@feathersjs/feathers'
import * as authentication from '@feathersjs/authentication'
import hook from '../../hook'
// Don't remove this comment. It's needed to format import lines nicely.

const {authenticate} = authentication.hooks

export default {
  before: {
    all: [authenticate('jwt')],
    find: [
      hook.userQuery(),
      hook.selectList,
      (d: HookContext) => {
        const query = d.params.query ?? {}
        if (!query?.$isSchool) {
          hook.sysQuery()(d)
        } else {
          delete query.$isSchool
        }

        if (!query.pid) query.pid = null // 默认只获取主服务包，排除Lecture包
      },
    ],
    get: [hook.toClass],
    create: [
      /** @transactional | `params` 可能包含一个 Mongoose session，所有数据库调用以及新添加的嵌套函数必须使用该 session。*/
    ],
    update: [hook.disable],
    patch: [hook.sysQuery(), hook.toClass],
    remove: [hook.sysQuery()],
  },

  after: {
    all: [],
    find: [
      async (d: HookContext) => {
        if (d.result)
          for (const o of d.result.data) {
            await d.service.extUser(o)
          }
        if (isDev) d.result.query = d.params.query
      },
    ],
    get: [
      async (d: HookContext) => {
        if (hook.classExist(d)) return d
        await d.service.extUser(d.result)
      },
    ],
    create: [
      /** @transactional | `params` 可能包含一个 Mongoose session，所有数据库调用以及新添加的嵌套函数必须使用该 session。*/
    ],
    update: [],
    patch: [
      async (d: HookContext) => {
        const {_id, total, used, status, snapshot} = d.result
        // 次数用完了自动更新状态
        if (used === total && status) await d.service.patch(_id, {status: false})
        // status 变化的时候, 服务包有效统计更新
        if (Acan.isDefined(d.data.status) && d.data.status !== status) {
          await d.app.service('service-pack').incCount(snapshot._id, {'count.valid': status ? 1 : -1})
        }
      },
    ],
    remove: [],
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: [],
  },
}
