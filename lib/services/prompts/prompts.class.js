"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Prompts = void 0;
const feathers_mongoose_1 = require("feathers-mongoose");
const errors_1 = require("@feathersjs/errors");
class Prompts extends feathers_mongoose_1.Service {
    //eslint-disable-next-line @typescript-eslint/no-unused-vars
    constructor(options, app) {
        super(options);
        this.app = app;
        this.selectList = [
            'uid',
            'unit',
            'curriculum',
            'service',
            'subjects',
            'sales',
            'used',
            'pages._id',
            'pages.pic',
            'pages.size',
            'questions._id',
            'questions.type',
        ];
    }
    // 生成快照
    async snapshot({ _id }, params) {
        const snapshot = await this.app.service('prompts').Model.findById(_id);
        if (!snapshot)
            return Promise.reject(new errors_1.NotFound());
        return Acan.clone(snapshot);
    }
    async extUser(one, params) {
        one.userInfo = await this.app.service('users').uidToInfo(one.uid);
    }
    async _Find(query, params) {
        const rs = { total: 0, skip: query.$skip || 0, limit: query.$limit || 10 };
        rs.total = await this.Model.count(query);
        rs.data = Acan.clone(await this.Model.find(query).select(query.$select).sort({ _id: -1 }).skip(rs.skip).limit(rs.limit));
        for (const o of rs.data) {
            await this.extUser(o);
        }
        if (isDev)
            rs.query = query;
        return rs;
    }
    // 认证精品课列表
    async getCloudList(query, params) {
        var _a;
        const uid = (_a = params.user) === null || _a === void 0 ? void 0 : _a._id;
        query.publish = true;
        // const unitSelect = this.app.service('unit').selectList.map((v: String) => 'unitSnapshot.' + v)
        query.$select = [...this.selectList];
        const rs = await this._Find(query, params);
        // if (!query.$skip || query.$skip == 0) {
        //   query.importUsers = uid
        //   const data: any = await this.Model.find(query).select(query.$select).limit(1000).lean()
        //   if (data) {
        //     for (const o of data) {
        //       await this.extUser(o)
        //     }
        //     rs.data.unshift(...data)
        //   }
        //   rs.has = data.length
        // }
        return rs;
    }
}
exports.Prompts = Prompts;
