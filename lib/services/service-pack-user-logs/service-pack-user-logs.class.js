"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServicePackUserLogs = void 0;
const feathers_mongoose_1 = require("feathers-mongoose");
class ServicePackUserLogs extends feathers_mongoose_1.Service {
    //eslint-disable-next-line @typescript-eslint/no-unused-vars
    constructor(options, app) {
        super(options);
    }
}
exports.ServicePackUserLogs = ServicePackUserLogs;
